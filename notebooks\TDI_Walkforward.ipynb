{"cells": [{"cell_type": "code", "execution_count": null, "id": "d34bd022", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import vectorbt as vbt\n", "import pandas_ta as ta\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objects as go\n", "\n", "# =============== CONFIG ===============\n", "SYMBOLS = [\"BTC-USD\", \"ETH-USD\", \"SOL-USD\", \"AVAX-USD\", \"MATIC-USD\"]\n", "START_DATE = \"2020-01-01\"\n", "END_DATE   = \"2024-07-13\"\n", "TIMEFRAME  = \"1d\"\n", "REB_FREQ   = \"ME\"      # month-end rebalance\n", "\n", "INIT_CASH = 10_000\n", "FEE       = 0.000045\n", "\n", "# Vol-adjusted sizing params\n", "ATR_PERIOD = 14\n", "TARGET_RISK_PCT = 0.02   # 2% of equity risked per trade\n", "\n", "# TDI\n", "RSI_PERIOD      = 21\n", "TDI_FAST_PERIOD = 2\n", "TDI_SLOW_PERIOD = 7\n", "\n", "vbt.settings.set_theme('dark')\n"]}, {"cell_type": "code", "execution_count": null, "id": "40fe6265", "metadata": {}, "outputs": [], "source": ["# =============== DATA ===============\n", "print(\"Downloading data …\")\n", "ohlcv = vbt.YFData.download(SYMBOLS, start=START_DATE, end=END_DATE, interval=TIMEFRAME).get()\n", "high, low, close, open_, volume, _div, _split = ohlcv\n", "print(f\"Loaded {len(close)} rows for {len(SYMBOLS)} assets\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "f8e51829", "metadata": {}, "outputs": [], "source": ["# ========== WALK-FORWARD SELECTION (monthly) ==========\n", "\n", "def monthly_selection_df(close_df: pd.DataFrame, vol_df: pd.DataFrame) -> pd.DataFrame:\n", "    sel = pd.DataFrame(False, index=close_df.index, columns=close_df.columns)\n", "    month_ends = close_df.resample(REB_FREQ).last().index\n", "    for i in range(len(month_ends) - 1):\n", "        train_end = month_ends[i]\n", "        test_start = train_end + pd.Timedelta(days=1)\n", "        test_end   = month_ends[i+1]\n", "\n", "        hist_close = close_df.loc[:train_end]\n", "        hist_vol   = vol_df.loc[:train_end]\n", "        if len(hist_close) < 252:\n", "            continue\n", "\n", "        returns_12m = hist_close.pct_change(252, fill_method=None).iloc[-1]\n", "        returns_1m  = hist_close.pct_change(21,  fill_method=None).iloc[-1]\n", "        mom_score   = returns_12m - returns_1m\n", "\n", "        vol_ratio = hist_vol.iloc[-20:].mean() / hist_vol.iloc[-60:-20].mean()\n", "        score = mom_score.fillna(0)*0.7 + vol_ratio.fillna(0)*0.3\n", "        best_symbol = score.idxmax()\n", "\n", "        sel.loc[test_start:test_end, best_symbol] = True\n", "    return sel\n", "\n", "selection = monthly_selection_df(close, volume)\n", "print(\"Selection mask built (exactly one asset True each month where enough history).\")"]}, {"cell_type": "code", "execution_count": null, "id": "51d68014", "metadata": {}, "outputs": [], "source": ["\n", "# =============== TDI CALCULATION ===============\n", "print(\"Calculating TDI across assets …\")\n", "\n", "def tdi_components(df_close: pd.DataFrame):\n", "    rsi_df   = df_close.apply(lambda s: ta.rsi(s, length=RSI_PERIOD))\n", "    fast_df  = rsi_df.apply(lambda s: ta.sma(s, length=TDI_FAST_PERIOD))\n", "    slow_df  = rsi_df.apply(lambda s: ta.sma(s, length=TDI_SLOW_PERIOD))\n", "    return fast_df, slow_df\n", "\n", "fast_ma, slow_ma = tdi_components(close)\n", "\n", "# =============== SIGNALS ===============\n", "entries = (fast_ma > slow_ma) & (fast_ma.shift(1) <= slow_ma.shift(1)) & selection\n", "exits_cross = (fast_ma < slow_ma) & (fast_ma.shift(1) >= slow_ma.shift(1))\n", "exits = exits_cross | (~selection)  # exit if TDI cross down or asset no longer selected\n", "\n", "# =============== PORTFOLIO ===============\n", "# ===== VOLATILITY-ADJUSTED SIZING =====\n", "# ATR per asset\n", "atr = vbt.talib('ATR').run(high, low, close, timeperiod=ATR_PERIOD).real\n", "atr.columns = close.columns  # ensure identical columns\n", "\n", "# Position size in percent of equity so that ATR move == TARGET_RISK_PCT of equity\n", "# size_percent = target_risk * price / ATR, capped to 100% of equity\n", "size_percent = (TARGET_RISK_PCT * close / atr).clip(upper=1.0)\n", "\n", "# Build size DataFrame: percent on entry, 1.0 on exit to flatten\n", "size_df = pd.DataFrame(0.0, index=close.index, columns=close.columns)\n", "size_df = size_percent.where(entries, 0.0)\n", "size_df[exits] = 1.0\n", "\n", "# Re-create portfolio with correct sizing\n", "portfolio = vbt.Portfolio.from_signals(\n", "    close=close,\n", "    entries=entries,\n", "    exits=exits,\n", "    size=size_df,\n", "    size_type='percent',\n", "    init_cash=INIT_CASH,\n", "    fees=FEE,\n", "    freq=TIMEFRAME,\n", ")\n", "\n", "print(\"\\n===== Walk-Forward Stats (Vol-Adjusted Sizing) =====\")\n", "print(portfolio.stats())\n"]}, {"cell_type": "code", "execution_count": null, "id": "33046ce4", "metadata": {}, "outputs": [], "source": ["total_equity = portfolio.value().sum(axis=1)\n", "equity_fig = total_equity.vbt.plot(title='Total Equity Curve')\n", "equity_fig.update_layout(xaxis_rangeslider_visible=False)\n", "equity_fig.show()\n", "\n", "# Per-symbol plots\n", "for sym in SYMBOLS:\n", "    sym_port = portfolio[sym]\n", "    fig = sym_port.plot(subplot_settings={'orders': {'close_trace_kwargs': {'visible': False}}})\n", "\n", "    # OHLCV\n", "    ohlcv_df = pd.DataFrame({\n", "        'Open': open_[sym],\n", "        'High': high[sym],\n", "        'Low': low[sym],\n", "        'Close': close[sym],\n", "        'Volume': volume[sym]\n", "    })\n", "    fig = ohlcv_df.vbt.ohlcv.plot(\n", "        plot_type='candlestick', fig=fig, show_volume=False, xaxis_rangeslider_visible=False\n", "    )\n", "\n", "    # TDI\n", "    tdi_df = pd.DataFrame({\n", "        'TDI Fast': fast_ma[sym],\n", "        'TDI Slow': slow_ma[sym]\n", "    })\n", "    fig = tdi_df.vbt.plot(fig=fig)\n", "\n", "    fig.update_layout(width=None, height=800, title=f\"TDI Strategy - {sym}\")\n", "    fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}