{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt\n", "import pandas as pd\n", "import pandas_ta as ta\n", "import sys,os\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "\n", "from exchange import fetch_and_prepare_data\n", "import plotly.graph_objects as go\n", "import math"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = fetch_and_prepare_data(\"BTC/USDC:USDC\")\n", "print(data.columns.tolist())\n", "\n", "listt= [1,2,3,4,5,6,7,8,9,10]\n", "\n", "listt[::2]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}