# VectorBT: A Comprehensive Guide

## Introduction

VectorBT is a powerful Python package designed for quantitative analysis and backtesting trading strategies. It operates on pandas and NumPy objects, leveraging Numba for high-performance computations. This makes it exceptionally fast and scalable for backtesting trading strategies across multiple parameters, assets, and time periods.

The library's name "VectorBT" reflects its core design philosophy: vectorized backtesting. Unlike traditional object-oriented backtesting frameworks that simulate trades sequentially, vectorbt processes entire arrays of data simultaneously, resulting in significant performance improvements.

## Installation

```python
pip install vectorbt
```

## Fonctionnement de VectorBT

### Philosophie vectorisée

VectorBT's vectorized approach is fundamentally different from traditional event-driven backtesting:

- **Traditional Backtesting**: Processes trades sequentially, simulating the passage of time (slower but intuitive)
- **Vectorized Backtesting**: Processes entire arrays of data simultaneously (much faster but requires different thinking)

This approach allows vectorbt to test thousands of parameter combinations in seconds, making it ideal for strategy optimization and research.

### Composants cœur (Indicators, Signals, Portfolio, Records)

1. **Indicators**: Technical indicators like moving averages, RSI, etc.
2. **Signals**: Entry and exit points for trades
3. **Portfolio**: Simulates portfolio performance based on signals
4. **Records**: Detailed information about trades, positions, and orders

## Sources de données

– YFData, CCXT, Twelve Data, autres

## Simulation de portefeuille

– Création : from_signals, from_orders, from_order_func

– Réglages (frais, cash, slippage, direction, etc.)

## Visualisation & Plotting

### Bibliothèque de kwargs (plot_configs.py) – référence unique

Pour éviter les redondances, centralisez tous vos paramètres dans un fichier dédié :`plot_configs.py`. Exemple :

```python
"""plot_configs.py – dictionnaires Plotly réutilisables"""
import plotly.graph_objects as go

# --- Mise en page générale ------------------------------------------------
BASE_LAYOUT = dict(
    template="plotly_dark",
    height=600,
    margin=dict(l=60, r=30, t=40, b=40),
    colorway=["#00E396", "#0090FF", "#FEB019", "#FF4560"],
)

# --- Axes -----------------------------------------------------------------
AXES = dict(
    showgrid=True, gridwidth=0.5, gridcolor="rgba(255,255,255,0.1)",
    showspikes=True, spikemode="across", spikecolor="rgba(200,200,200,0.4)",
    spikethickness=1, ticks="outside", ticklen=5,
    tickcolor="rgba(200,200,200,0.7)",
)

# --- Légende --------------------------------------------------------------
LEGEND = dict(
    orientation="h", yanchor="bottom", y=1.02,
    xanchor="right", x=1, bgcolor="rgba(0,0,0,0)",
    bordercolor="rgba(255,255,255,0.2)", borderwidth=1,
)

# --- Sous-tracés ----------------------------------------------------------
SUBPLOTS = {
    "orders": {
        "close_trace_kwargs": {"visible": False}
    }
}

# --- OHLC -----------------------------------------------------------------
OHLC = dict(
    plot_type="candlestick", show_volume=False, xaxis_rangeslider_visible=False,
    ohlc_kwargs=dict(
        increasing_line_color="rgba(0,200,0,0.8)",
        decreasing_line_color="rgba(200,30,0,0.8)",
    ),
)

# --- Exemple d’utilisation ----------------------------------------------
from plot_configs import BASE_LAYOUT, AXES, LEGEND, SUBPLOTS, OHLC

fig = portfolio.plot(subplot_settings=SUBPLOTS, **BASE_LAYOUT)
fig = price.vbt.ohlcv.plot(fig=fig, **OHLC)
fig.update_xaxes(**AXES)
fig.update_yaxes(**AXES)
fig.update_layout(legend=LEGEND, title="Orders & Candlestick Overlay",width=None,height=None).show()
```
mettre with=None,height=None est important pour avoir le plein écran car ce n'est pas initlisé de base dans vbt
Les avantages :
1. Une **source unique de vérité** pour vos réglages graphiques.
2. Des **imports clairs** : `from plot_configs import BASE_LAYOUT`.
3. La possibilité d’ajuster un paramètre (ex. `template`) pour tous les graphiques du projet.

### Plotting Recipes

Here are some quick recipes for common plotting tasks in vectorbt.

#### Comparing Multiple Strategy Equity Curves

The best way to compare the performance of multiple portfolios is to combine their equity curves into a single DataFrame and plot it.

```python
# Assumes portfolio_A and portfolio_B are existing vbt.Portfolio objects
strategies = {
    'Strategy A': portfolio_A, 
    'Strategy B': portfolio_B
}
# Combine their values into a single DataFrame
comparison_data = pd.DataFrame({
    name: pf.value() for name, pf in strategies.items()
})
# Plot using vectorbt's accessor
comparison_fig = comparison_data.vbt.plot()
comparison_fig.update_layout(
    title="Strategy Performance Comparison",
    yaxis_title="Portfolio Value ($)"
).show()
```

#### Plotting a Histogram of Returns

To plot a distribution of returns, use `plotly.graph_objects.Histogram`. The `.vbt.hist()` accessor does not exist on a Series.

```python
import plotly.graph_objects as go

returns_fig = go.Figure()
returns_fig.add_trace(go.Histogram(
    x=portfolio_A.returns(),
    name='Strategy A',
    opacity=0.7
))
returns_fig.add_trace(go.Histogram(
    x=portfolio_B.returns(),
    name='Strategy B',
    opacity=0.7
))
returns_fig.update_layout(
    title_text="Daily Returns Distribution",
    barmode='overlay' # Overlay histograms for comparison
).show()
```

#### Creating Subplots

To create figures with multiple subplots, import and use `make_subplots` directly from `plotly.subplots`. When adding traces, use `add_trace_kwargs` to specify the row and column.

```python
from plotly.subplots import make_subplots

# Create a figure with 2 rows
# Assumes 'price' and 'indicator' are pandas Series
fig = make_subplots(rows=2, cols=1, shared_xaxes=True)

# Add traces to specific subplots
price.vbt.plot(fig=fig, add_trace_kwargs=dict(row=1, col=1))
indicator.vbt.plot(fig=fig, add_trace_kwargs=dict(row=2, col=1))

fig.show()
```

### Recettes rapides :

• Performance globale • Overlay ordres + chandelier • Trade analysis

### Exemples d’annotations / formes / palettes

```python
# --- Axe X et Y ----------------------------------------------------------
axis_kwargs = dict(
    showgrid=True,
    gridwidth=0.5,
    gridcolor='rgba(255,255,255,0.1)',
    showspikes=True,
    spikemode='across',
    spikecolor='rgba(200,200,200,0.4)',
    spikethickness=1,
    ticks='outside',
    ticklen=5,
    tickcolor='rgba(200,200,200,0.7)'
)

# Utilisation :
fig.update_xaxes(**axis_kwargs)
fig.update_yaxes(**axis_kwargs)

# --- Légende -------------------------------------------------------------
legend_kwargs = dict(
    orientation='h',
    yanchor='bottom', y=1.02,
    xanchor='right',  x=1,
    bgcolor='rgba(0,0,0,0)',  # fond transparent
    bordercolor='rgba(255,255,255,0.2)',
    borderwidth=1
)
fig.update_layout(legend=legend_kwargs)

# --- Marges et padding ---------------------------------------------------
margin_kwargs = dict(l=60, r=30, t=40, b=40)
fig.update_layout(margin=margin_kwargs)

# --- Annotations ---------------------------------------------------------
annotations = [
    dict(x='2024-01-01', y=price.loc['2024-01-01'],
         xref='x', yref='y',
         text="Nouvelle année",
         showarrow=True, arrowhead=2,
         ax=0, ay=-40)
]
fig.update_layout(annotations=annotations)

# --- Formes (zones, lignes horizontales…) -------------------------------
shape_kwargs = dict(
    type='rect',
    xref='x', yref='paper',
    x0='2024-03-01', x1='2024-03-15',
    y0=0, y1=1,
    fillcolor='rgba(255,0,0,0.1)',
    line=dict(width=0)
)
fig.add_shape(**shape_kwargs)

# --- Palette de couleurs -------------------------------------------------
colorway_kwargs = dict(colorway=['#00E396', '#0090FF', '#FEB019', '#FF4560'])
fig.update_layout(**colorway_kwargs)

# --- Exportation / responsive -------------------------------------------
export_kwargs = dict(
    autosize=True,
    height=600,
    width=1200,
    paper_bgcolor='rgba(0,0,0,0)',
    plot_bgcolor='rgba(0,0,0,0)'
)
fig.update_layout(**export_kwargs)
```

## Exemples de stratégies

### SMA Crossover

```python
import vectorbt as vbt
import pandas as pd

# Fetch data
price = vbt.YFData.download('SPY').get('Close')

# Calculate fast and slow moving averages
fast_ma = vbt.MA.run(price, 10)
fast_ma = fast_ma.ma
slow_ma = vbt.MA.run(price, 50)
slow_ma = slow_ma.ma

# Generate entry and exit signals
entries = fast_ma.vbt.crossed_above(slow_ma)
exits = fast_ma.vbt.crossed_below(slow_ma)

# Create portfolio
portfolio = vbt.Portfolio.from_signals(
    close=price,
    entries=entries,
    exits=exits,
    init_cash=10000,
    fees=0.001
)

# Display performance metrics
print(portfolio.stats())

# Plot results
portfolio.plot().show()
```

Voir section 6 pour la visualisation

### RSI Mean Reversion

```python
import vectorbt as vbt
import pandas as pd

# Fetch data
price = vbt.YFData.download('SPY').get('Close')

# Calculate RSI
rsi = vbt.RSI.run(price, window=14).rsi

# Generate entry and exit signals
entries = rsi < 30  # Buy when RSI is below 30 (oversold)
exits = rsi > 70    # Sell when RSI is above 70 (overbought)

# Create portfolio
portfolio = vbt.Portfolio.from_signals(
    close=price,
    entries=entries,
    exits=exits,
    init_cash=10000,
    fees=0.001
)

# Display performance metrics
print(portfolio.stats())

# Plot results
portfolio.plot().show()
```

Voir section 6 pour la visualisation

### Grid Trading (concept)

Grid trading is a strategy that places buy and sell orders at predetermined price levels, creating a grid of orders. Here's how you might structure a grid trading implementation with vectorbt, focusing on the styling and methods rather than a specific algorithm:

```python
import vectorbt as vbt
import pandas as pd
import numpy as np

# Configuration pattern - keeping settings organized in a dictionary
CONFIG = {
    'grid': {'levels': 5, 'range_percentage': 0.05},  # Grid parameters
    'position': {'size': 1},  # Position sizing
    'account': {'initial_cash': 10000, 'fees': 0.001},  # Account settings
    'portfolio': {'freq': '1d', 'direction': 'both'}  # Portfolio settings
}

# Fetch data using vectorbt's data fetching capabilities
price = vbt.YFData.download('BTC-USD').get('Close')

# Example of how you might define grid levels
min_price = price.min()
max_price = price.max()
grid_width = (max_price - min_price) / (CONFIG['grid']['levels'] - 1)

# Create a DataFrame to store grid levels
data = pd.DataFrame(index=price.index)
data['close'] = price

# Generate grid levels
for i in range(CONFIG['grid']['levels']):
    data[f'grid_{i}'] = min_price + grid_width * i

# Signal generation example (simplified conceptual approach)
# In a real implementation, you would need more sophisticated logic
entries = pd.Series(False, index=data.index)
exits = pd.Series(False, index=data.index)

# Example of how signals might be generated
# Note: This is a simplified example and not a complete implementation
for i in range(1, CONFIG['grid']['levels']):
    # Example condition: price crosses below a grid level
    entries = entries | (data['close'] < data[f'grid_{i}']) & (data['close'].shift(1) >= data[f'grid_{i}'])
    # Example condition: price crosses above a grid level
    exits = exits | (data['close'] > data[f'grid_{i-1}']) & (data['close'].shift(1) <= data[f'grid_{i-1}'])

# Create portfolio using vectorbt's Portfolio class
portfolio = vbt.Portfolio.from_signals(
    close=data['close'],
    entries=entries,
    exits=exits,
    size=CONFIG['position']['size'],
    init_cash=CONFIG['account']['initial_cash'],
    fees=CONFIG['account']['fees'],
    freq=CONFIG['portfolio']['freq'],
    direction=CONFIG['portfolio']['direction']
)

# Display statistics and visualize results
print(portfolio.stats())

# Set theme to dark and create visualizations with fullscreen layout
vbt.settings.set_theme('dark')

# Portfolio plot with indicators and OHLCV data
fig = portfolio.plot(subplot_settings={'orders': {'close_trace_kwargs': {'visible': False}}})

# Add moving average to the plot
data['ma'].vbt.plot(fig=fig, trace_kwargs=dict(name='MA', line=dict(color='orange', width=1)))

# Add OHLCV candlestick chart
fig = data.vbt.ohlcv.plot(
    plot_type='candlestick',
    show_volume=False,
    xaxis_rangeslider_visible=False,
    ohlc_kwargs=dict(
        increasing_line_color='rgba(101, 230, 101, 0.8)',
        decreasing_line_color='rgba(230, 30, 101, 0.8)'
    ),
    fig=fig
)
fig.update_layout(width=None, height=None, title_text="Grid Trading Strategy - Backtest").show()

# Trade analysis with specific subplots and custom settings
portfolio.plot(
    subplots=['trades', 'trade_pnl'],
    subplot_settings={
        'trades': dict(title='Trades'),
        'trade_pnl': dict(title='Trade PnL')
    }
).update_layout(
    width=None, height=None, 
    title_text="Grid Strategy - Performance Analysis"
).show()

# Note: You can list available subplot and trace names using:
# print(portfolio.subplots) # List available subplots
# print(fig.data) # List all traces in a figure
```

Voir section 6 pour la visualisation

## Optimisation de paramètres

One of vectorbt's most powerful features is its ability to efficiently test strategies across multiple parameters:

```python
import vectorbt as vbt
import pandas as pd
import numpy as np

# Fetch data
price = vbt.YFData.download('SPY').get('Close')

# Define parameter ranges for fast and slow moving averages
fast_windows = np.arange(5, 51, 5)  # 5, 10, 15, ..., 50
slow_windows = np.arange(20, 101, 10)  # 20, 30, 40, ..., 100

# Run MA Crossover for all combinations of parameters
fast_ma = vbt.MA.run(price, window=fast_windows)
fast_ma = fast_ma.ma
slow_ma = vbt.MA.run(price, window=slow_windows)
slow_ma = slow_ma.ma

# Generate entry and exit signals for all parameter combinations
entries = fast_ma.vbt.crossed_above(slow_ma)
exits = fast_ma.vbt.crossed_below(slow_ma)

# Create portfolio for all parameter combinations
portfolio = vbt.Portfolio.from_signals(
    close=price,
    entries=entries,
    exits=exits,
    init_cash=10000,
    fees=0.001
)

# Find the best parameter combination based on Sharpe ratio
metrics = portfolio.metrics(['total_return', 'sharpe_ratio', 'max_drawdown'])
print(metrics)

# Get the parameter combination with the highest Sharpe ratio
best_idx = metrics['sharpe_ratio'].idxmax()
print(f"Best parameters: Fast MA = {fast_windows[best_idx[0]]}, Slow MA = {slow_windows[best_idx[1]]}")

# Plot the performance of the best strategy
best_portfolio = portfolio.iloc[best_idx[0], best_idx[1]]
best_portfolio.plot().show()
```

### Sweep vectorisé

### Walk-Forward

## Cadre de stratégie réutilisable (classes abstraites + dataclasses)

Here's an example of a flexible strategy framework using vectorbt:

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple
import pandas as pd
import vectorbt as vbt
from dataclasses import dataclass

@dataclass
class MarketData:
    """Container for market data"""
    symbol: str
    timeframe: str
    open: pd.Series
    high: pd.Series
    low: pd.Series
    close: pd.Series
    volume: pd.Series
    
    @property
    def ohlcv(self) -> pd.DataFrame:
        """Return OHLCV DataFrame"""
        return pd.DataFrame({
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        })

@dataclass
class StrategyConfig:
    """Base configuration for strategies"""
    name: str
    version: str = "1.0"
    author: str = ""
    description: str = ""

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies"""
    
    # Class-level parameter grid for optimization
    param_grid: Dict[str, List] = {}
    
    # Strategy metadata
    config: StrategyConfig = None
    
    def __init__(self, **params):
        """Initialize strategy with parameters"""
        # Set parameters as attributes
        for key, value in params.items():
            setattr(self, key, value)
        
        # Initialize indicator cache
        self._indicator_cache = {}
        
        # Validate parameters
        self.validate_parameters()
    
    def validate_parameters(self):
        """Override to add parameter validation"""
        pass
    
    @abstractmethod
    def calculate_indicators(self, data: MarketData) -> Dict[str, pd.Series]:
        """Calculate all indicators needed for the strategy"""
        pass
    
    @abstractmethod
    def generate_signals(self, data: MarketData, indicators: Dict[str, pd.Series]) -> Tuple[pd.Series, pd.Series]:
        """Generate entry and exit signals based on indicators"""
        pass
    
    def entries(self, data: MarketData) -> pd.Series:
        """Generate entry signals - called by backtester"""
        indicators = self.calculate_indicators(data)
        entries, _ = self.generate_signals(data, indicators)
        return entries
    
    def exits(self, data: MarketData) -> pd.Series:
        """Generate exit signals - called by backtester"""
        indicators = self.calculate_indicators(data)
        _, exits = self.generate_signals(data, indicators)
        return exits

# Example implementation
class MACrossoverStrategy(BaseStrategy):
    """Simple MA Crossover"""
    
    config = StrategyConfig(
        name="MA Crossover",
        version="2.0",
        description="Crosses of fast and slow moving averages"
    )
    
    param_grid = {
        'fast_period': [10, 20, 30],
        'slow_period': [50, 100, 200],
        'ma_type': ['SMA', 'EMA']
    }
    
    def validate_parameters(self):
        if self.fast_period >= self.slow_period:
            raise ValueError("Fast period must be less than slow period")
    
    def calculate_indicators(self, data: MarketData) -> Dict[str, pd.Series]:
        """Calculate moving averages"""
        if self.ma_type == 'SMA':
            fast_ma = vbt.MA.run(data.close, self.fast_period, short_name='fast').ma.squeeze()
            slow_ma = vbt.MA.run(data.close, self.slow_period, short_name='slow').ma.squeeze()
        else:  # EMA
            fast_ma = vbt.MA.run(data.close, self.fast_period, ewm=True, short_name='fast').ma.squeeze()
            slow_ma = vbt.MA.run(data.close, self.slow_period, ewm=True, short_name='slow').ma.squeeze()
        
        return {
            'fast_ma': fast_ma,
            'slow_ma': slow_ma
        }
    
    def generate_signals(self, data: MarketData, indicators: Dict[str, pd.Series]) -> Tuple[pd.Series, pd.Series]:
        """Generate crossover signals"""
        fast_ma = indicators['fast_ma']
        slow_ma = indicators['slow_ma']
        
        # Entry when fast crosses above slow
        entries = (fast_ma > slow_ma) & (fast_ma.shift(1) <= slow_ma.shift(1))
        
        # Exit when fast crosses below slow
        exits = (fast_ma < slow_ma) & (fast_ma.shift(1) >= slow_ma.shift(1))
        
        return entries, exits
```

## Performance (VB vs implémentations Pandas)

VectorBT significantly outperforms traditional backtesting frameworks in terms of speed. Here's a comparison of computing a rolling Z-score:

### Pandas Implementation

```python
def pandas_zscore(series, window):
    r = series.rolling(window=window)
    m = r.mean()
    s = r.std(ddof=0)
    return (series - m) / s
```

### VectorBT Implementation

```python
@njit
def nb_zscore(arr, window):
    out = np.empty_like(arr)
    for i in range(arr.shape[0]):
        if i < window:
            out[i] = np.nan
        else:
            window_slice = arr[i-window:i]
            mean = np.mean(window_slice)
            std = np.std(window_slice)
            out[i] = (arr[i] - mean) / std
    return out
```

For a series with 10,000 elements and a window of 100, vectorbt's implementation can be 10-100x faster.


### **I. Optimisation du Portefeuille avec VectorBT**

1. **Diversification Multi-Assets Avancée**
   - Implémentez une **matrice de corrélation dynamique** entre les actifs :
   ```python
   returns = full_portfolio.returns()
   correlation_matrix = returns.vbt.returns.corr()
   heatmap = correlation_matrix.vbt.heatmap()
   ```
   - Utilisez `vbt.Portfolio.from_orders` avec `group_by=True` pour une allocation pondérée par volatilité (risk parity)

2. **Walk-Forward Optimization Automatisée**
   - Remplacez l'optimisation manuelle par le module `vbt.PfOrc` :
   ```python
   walkforward = vbt.PfOrc.walk(
       n=30,  # Nombre de fenêtres
       window_len=365,
       set_lens=(90, 180),
       lag=30
   )
   ```

3. **Position Sizing Dynamique**
   - Intégrez des méthodes scientifiques de sizing :
   ```python
   # Kelly Criterion
   kelly_size = returns.vbt.kelly_size()
   # Optimal Sizing via Sharpe Ratio Maximization
   optimal_size = returns.vbt.optimal_size()
   ```

4. **Analyse de Sensibilité Paramétrique**
   - Utilisez les grilles paramétriques natives :
   ```python
   param_grid = np.array(np.meshgrid(
       np.arange(10, 100, 10),  # windows
       np.linspace(0.5, 0.9, 5),  # vol_quantiles
       np.arange(1.0, 3.0, 0.5)  # z_thresholds
   ), dtype=object).T.reshape(-1, 3)
   ```

5. **Gestion du Risque Portfolio-Level**
   - Implémentez un stop-loss global :
   ```python
   portfolio = vbt.Portfolio.from_signals(
       ...,
       stop_loss=0.05,  # Stop global
       stop_type='percent'
   )
   ```

---

### **II. Visualisations Avancées avec VectorBT**

1. **Dashboard Interactif Intégré**
   ```python
   full_portfolio.plot(
       subplots=['orders', 'trade_pnl', 'cum_returns', 'drawdowns'],
       active_subplots=True,
       width=1400
   ).show()
   ```

2. **Heatmaps de Performance Paramétrique**
   ```python
   results_df.vbt.heatmap(
       x_level='window',
       y_level='z_threshold',
       slider_level='vol_quantile',
       trace_kwargs=dict(colorbar=dict(title='Sharpe'))
   )
   ```

3. **Analyse Comparative Automatisée**
   ```python
   benchmark_rets = buy_hold.pct_change()
   vbt.plotting.plot_multiple(
       [full_portfolio.returns(), benchmark_rets],
       labels=['Strategy', 'Benchmark'],
       performance_kwargs=dict(title="Performance Comparée")
   )
   ```

4. **Visualisation 3D des Paramètres**
   ```python
   fig = results_df.vbt.plot_3d(
       x='window',
       y='z_threshold',
       z='sharpe',
       trace_kwargs=dict(
           colorscale='Viridis',
           opacity=0.7
       )
   )
   fig.update_layout(scene=dict(zaxis_title='Sharpe Ratio'))
   ```

5. **Analyse Monte Carlo Intégrée**
   ```python
   mc_sims = full_portfolio.vbt.monte_carlo(
       n=1000,
       time_hops=252
   )
   mc_sims.plot().show()
   ```

---

### **III. Optimisations Techniques Avancées**

1. **Caching des Données Optimisé**
   ```python
   data = vbt.YFData.download(
       symbols,
       missing_index='drop',
       show_progress=True
   ).cache()
   ```

2. **Calculs Parallélisés**
   ```python
   results = vbt.run_parallel(
       param_grid,
       run_func=run_backtest,
       engine='ray'
   )
   ```

3. **Custom Metrics**
   ```python
   vbt.settings.portfolio['metrics'] = {
       'calmar_ratio': vbt.CalmarRatio,
       'tail_ratio': vbt.TailRatio
   }
   ```

4. **Analyse de Liquidité**
   ```python
   liquidity = data.get('volume').rolling(50).mean()
   portfolio = vbt.Portfolio.from_signals(
       ...,
       size=np.where(liquidity > threshold, base_size, base_size * 0.5)
   )
   ```

5. **Frais en Fonction du Volume**
   ```python
   def dynamic_fees(volume):
       return np.where(volume > 1e6, 0.0005, 0.001)
   ```

---

### **IV. Points d'Amélioration Clés**

1. **Transition vers l'API native VectorBT** :
   - Remplacer les boucles manuelles par des opérations vectorisées
   - Exploiter `vbt.IndicatorFactory` pour des indicateurs personnalisés

2. **Gestion du Temps Réel** :
   ```python
   portfolio = vbt.Portfolio.from_signals(..., freq='15min')
   resampled = portfolio.resample('D')
   ```

3. **Analyse Sensible aux Coûts** :
   ```python
   sensitivity = portfolio.sensitivity(
       param='fees',
       param_range=np.linspace(0.0001, 0.01, 50)
   )
   ```

4. **Integration Machine Learning** :
   ```python
   from vbt.portfolio import MLPortfolio
   ml_portfolio = MLPortfolio.from_xy(X, y)
   ```

Ces améliorations exploitent pleinement les capacités de VectorBT pour une analyse quantitative plus robuste, tout en optimisant les performances de calcul et la qualité des visualisations.