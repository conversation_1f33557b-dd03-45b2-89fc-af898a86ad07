from urllib.parse import urlencode
from decimal import Decimal
import time, hmac, hashlib, json, requests, pandas as pd

API_KEY = 'JonKv8M5bcU3XiC35h'
API_SECRET = 'WEFLjjYPmwtEpXExNxVdJJ9yCD3yhx3f2tU7'
BASE_URL = 'https://api-demo.bybit.com'
RECV_WINDOW = 5000

def format_quantity(quantity, quantity_step_string):
    """Formats a quantity according to the required step size and decimal places."""
    try:
        step_size = Decimal(quantity_step_string)
        # Determine required decimal places from the step size exponent
        decimal_places = abs(step_size.as_tuple().exponent) if step_size.is_finite() and step_size != 0 else 0
        quantity_decimal = Decimal(str(quantity))
        # Round down to the nearest step size
        rounded_quantity = (quantity_decimal // step_size) * step_size
        formatted_quantity = f'{rounded_quantity:.{decimal_places}f}'

        # Check if rounding resulted in zero for a non-zero input quantity
        if rounded_quantity <= 0 and quantity_decimal > 0 and quantity_decimal >= step_size:
            print(f"Warning: format_quantity rounded {quantity} to 0 w/ step {quantity_step_string}. Using original value.")
            return str(quantity) # Fallback to original quantity string if rounding fails unexpectedly
        return formatted_quantity
    except Exception as e:
        print(f"Error formatting quantity {quantity} w/ step {quantity_step_string}: {e}. Using original value.")
        return str(quantity) # Fallback in case of any exception

def generate_headers(payload_string):
    """Generates necessary headers for Bybit API requests including the signature."""
    timestamp_ms = str(int(time.time() * 1000))
    # Construct the string to sign
    signature_string = timestamp_ms + API_KEY + str(RECV_WINDOW) + payload_string
    # Create the HMAC SHA256 signature
    signature = hmac.new(bytes(API_SECRET, 'utf-8'), bytes(signature_string, 'utf-8'), hashlib.sha256).hexdigest()
    # Return the required headers
    return {
        'X-BAPI-API-KEY': API_KEY,
        'X-BAPI-TIMESTAMP': timestamp_ms,
        'X-BAPI-SIGN': signature,
        'X-BAPI-RECV-WINDOW': str(RECV_WINDOW),
        'Content-Type': 'application/json'
    }

def make_request(method, path, params=None, data=None):
    """Makes an HTTP request to the Bybit API, handles errors, and returns the result."""
    url = BASE_URL + path
    query_string = ""
    body_string = ""

    # Prepare query string for GET or body string for POST
    if params:
        query_string = urlencode(params)
        url += "?" + query_string
    if data:
        body_string = json.dumps(data)

    # Determine which part of the request contributes to the signature
    signature_payload = query_string if method == 'GET' else body_string
    headers = generate_headers(signature_payload)

    response = None # Initialize response to None
    try:
        # Execute the request based on the method
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, data=body_string)
        else:
            print(f"Error: Unsupported HTTP method: {method}")
            return None

        response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
        response_json = response.json()

        # Check Bybit's specific return code for success
        if response_json.get('retCode') != 0:
            print(f"Bybit API Error: Code={response_json.get('retCode')}, Msg='{response_json.get('retMsg')}' for {method} {path}")
            return None
        # Return the 'result' field on success
        return response_json.get('result')

    except requests.exceptions.RequestException as e:
        print(f"HTTP Request failed: {e}")
        return None
    except json.JSONDecodeError:
        # Log the text that failed to decode if response is available
        error_text = response.text if response else "No response text available"
        print(f"JSON Decode Error: Failed to parse response: {error_text}")
        return None
    except Exception as e:
        print(f"Unexpected error during API request to {path}: {e}")
        return None

def get_current_price(symbol):
    """Fetches the last traded price for a given symbol."""
    params = {'category': 'linear', 'symbol': symbol}
    result = make_request('GET', '/v5/market/tickers', params=params)

    if not result or not result.get('list'):
        print(f"Could not fetch price data for {symbol}.")
        return None
    try:
        # Extract the last price from the first item in the list
        last_price_str = result['list'][0].get('lastPrice')
        if not last_price_str:
            print(f"No lastPrice found for {symbol} in response.")
            return None
        return float(last_price_str)
    except (ValueError, TypeError, IndexError) as e:
        print(f"Error parsing price for {symbol}: {e}")
        return None
    except KeyError:
        print(f"KeyError accessing price data for {symbol}.")
        return None

def get_balance():
    """Fetches the unified account wallet balance."""
    params = {'accountType': 'UNIFIED'}
    result = make_request('GET', '/v5/account/wallet-balance', params=params)

    if not result or not result.get('list'):
        print("Failed to fetch balance information.")
        return None
    try:
        # Assuming balance info is in the first element of the list
        balance_data = result['list'][0]
        # Helper to safely convert balance fields to float
        def safe_float(key):
            return float(balance_data.get(key, '0') or '0')

        return {
            'totalWalletBalance': safe_float('totalWalletBalance'),
            'totalAvailableBalance': safe_float('totalAvailableBalance'),
            'totalMarginBalance': safe_float('totalMarginBalance')
        }
    except (ValueError, TypeError, IndexError) as e:
        print(f"Error parsing balance data: {e}")
        return None
    except KeyError:
        print("KeyError accessing balance data.")
        return None

def print_balance():
    """Fetches and prints formatted wallet balance information."""
    balance = get_balance()
    if not balance:
        print("Could not retrieve balance information.")
        return

    print("\n=== Wallet Balance (Unified Account) ===")
    print(f"Total Balance:     {balance['totalWalletBalance']:,.2f} USDT (Total asset value)")
    print(f"Margin Balance:    {balance['totalMarginBalance']:,.2f} USDT (Total Balance + Unrealized P&L; Used for liquidation checks)")
    print(f"Available Balance: {balance['totalAvailableBalance']:,.2f} USDT (Margin Balance - Used Margin; Funds usable for new positions)")
    print("=====================================\n")

def convert_usdc_to_contracts(amount, symbol):
    """Converts a USDC amount to the equivalent number of contracts based on current price."""
    current_price = get_current_price(symbol)
    if not current_price or current_price <= 0:
        # Error message printed within get_current_price or if price is invalid
        print(f"Cannot convert amount for {symbol} due to invalid price: {current_price}")
        return None
    return amount / current_price

def get_position(symbol):
    """Fetches position information for a specific symbol."""
    params = {'category': 'linear', 'symbol': symbol}
    result = make_request('GET', '/v5/position/list', params=params)

    # Check if result and list exist, and the list is not empty
    if result and result.get('list') and not None:
        return result['list'][0] # Assuming the first element is the relevant position
    else:
        print(f"Failed to fetch position data for {symbol}.")
        return None

def get_all_positions():
    """Fetches all non-zero positions for the linear category."""
    params = {'category': 'linear', 'settleCoin': 'USDT'}
    result = make_request('GET', '/v5/position/list', params=params)

    if not result or not result.get('list'):
        print("Could not fetch open positions list.")
        return [] # Return empty list if fetch fails or list is empty

    # Filter for positions with a non-zero size
    open_positions = []
    for position in result['list']:
        try:
            size = float(position.get('size', '0') or '0')
            if size != 0:
                open_positions.append(position)
        except (ValueError, TypeError):
            print(f"Warning: Could not parse size for position {position.get('symbol', 'N/A')}")
    return open_positions


def get_instrument_info(symbol):
    """Fetches instrument details like lot size filter for a symbol."""
    params = {'category': 'linear', 'symbol': symbol}
    result = make_request('GET', '/v5/market/instruments-info', params=params)

    if result and result.get('list'):
        return result['list'][0] # Assume first item contains the info

    print(f"Failed to fetch instrument info for {symbol}.")
    return None

def get_position_pnl(symbol):
    """Calculates the unrealised PNL for a given position symbol."""
    position_info = get_position(symbol)
    if not position_info:
        # Error/message handled in get_position
        return None
    try:
        # Use 'unrealizedPnl' consistent with Exchange.py, although Bybit API returns 'unrealisedPnl'
        unrealized_pnl_str = position_info.get('unrealisedPnl') # Fetch API key
        return float(unrealized_pnl_str or 0.0)
    except (ValueError, TypeError) as e:
        print(f"Error parsing PNL for {symbol}: {e}")
        return None
    except KeyError:
        # Standardize error message key reference
        print(f"KeyError accessing unrealizedPnl for {symbol}.")
        return None

def create_order(symbol, side, order_type, amount, price=None):
    """Creates a new order (Market or Limit)."""
    payload = {
        'category': 'linear',
        'symbol': symbol,
        'side': side.capitalize(),
        'orderType': order_type.capitalize(),
        'qty': str(amount) # Quantity must be a string (API uses 'qty')
    }
    # Add price only for Limit orders
    if order_type.lower() == 'limit' and price is not None:
        payload['price'] = str(price) # Price must also be a string

    result = make_request('POST', '/v5/order/create', data=payload)

    if result and result.get('orderid'):
        return result['orderid'] # Return the new order ID on success

    # Error message printed in make_request if API call failed
    print(f"Failed to create {side} {order_type} order for {amount} {symbol}.")
    return None

def cancel_order(order_id, symbol):
    """Cancels an existing open order."""
    payload = {
        'category': 'linear',
        'symbol': symbol,
        'orderid': order_id
    }
    result = make_request('POST', '/v5/order/cancel', data=payload)

    if result and result.get('orderid'):
        print(f"Successfully initiated cancel for order {order_id} ({symbol}). Result ID: {result['orderid']}")
        return result['orderid'] # Bybit returns the cancelled order ID on success

    # Error message printed in make_request if API call failed
    print(f"Failed to cancel order {order_id} for {symbol}.")
    return None

def fetch_order_status(order_id, symbol):
    """Fetches the current status of a specific order."""
    params = {'category': 'linear', 'symbol': symbol, 'orderid': order_id, 'limit': 1}
    result = make_request('GET', '/v5/order/history', params=params)

    if result and result.get('list'):
        try:
            # Extract status from the first (and only) order in the list
            return result['list'][0].get('orderStatus')
        except IndexError:
            print(f"Order history list unexpectedly empty for {order_id} ({symbol}).")
            return 'unknown'    
        except KeyError:
            print(f"KeyError accessing orderStatus for {order_id} ({symbol}).")
            return 'unknown'

    # Error message printed in make_request if API call failed
    print(f"Could not fetch status for order {order_id} ({symbol}).")
    return 'unknown'

def open_position(symbol, side, usdc_amount):
    """Opens a market position for a given symbol, side, and USDC amount."""
    # 1. Get current price
    current_price = get_current_price(symbol)
    if not current_price: 
        return None # Error handled in get_current_price

    # 2. Get instrument info for order constraints
    instrument_info = get_instrument_info(symbol)
    if not instrument_info: 
        return None # Error handled in get_instrument_info

    # 3. Extract order constraints (min quantity, step size)
    lot_size_filter = instrument_info.get('lotSizeFilter', {})
    min_order_quantity_string = lot_size_filter.get('minOrderQty', '0')
    quantity_step_string = lot_size_filter.get('qtyStep', '1')

    try:
        min_order_quantity = float(min_order_quantity_string)
    except (ValueError, TypeError):
        print(f"Error: Invalid minOrderQty ('{min_order_quantity_string}') received for {symbol}.")
        return None # Cannot proceed without valid minimum quantity

    # 4. Calculate contracts needed based on quote amount and price
    calculated_contracts = convert_usdc_to_contracts(usdc_amount, symbol)
    if not calculated_contracts: return None # Error handled in convert_usdc_to_contracts
    if calculated_contracts <= 0:
        print(f"Error: Calculated contracts ({calculated_contracts}) must be positive for {symbol}.")
        return None

    # 5. Check against minimum order quantity
    if calculated_contracts < min_order_quantity:
        print(f"Order quantity ({calculated_contracts}) is less than minimum ({min_order_quantity}) for {symbol}.")
        return None

    # 6. Format quantity according to step size
    formatted_quantity = format_quantity(calculated_contracts, quantity_step_string)
    if not formatted_quantity or float(formatted_quantity) <= 0:
        print(f"Error: Formatted quantity '{formatted_quantity}' is invalid for {symbol}.")
        return None

    # 7. Place the market order
    print(f"Attempting to open {side} position: {formatted_quantity} {symbol} (calculated: {calculated_contracts:.8f}, min: {min_order_quantity}, step: {quantity_step_string})")
    order_id = create_order(symbol, side, 'Market', amount=formatted_quantity)

    if order_id:
        print(f"Market open order placed successfully. Order ID: {order_id}")
    # else: Error handled in create_order

    return order_id

def close_position(symbol):
    """Closes an existing open position for the given symbol using a market order."""
    # 1. Get current position details
    position_info = get_position(symbol)
    if not position_info: return None # Error handled in get_position or means no position

    # 2. Parse position size and side
    try:
        size_string = position_info.get('size', '0')
        position_side = position_info.get('side') # 'Buy', 'Sell', or 'None'
        position_size = float(size_string)
    except (ValueError, TypeError) as e:
        print(f"Error parsing position size/side for {symbol}: {e}")
        return None
    except KeyError:
        print(f"KeyError accessing size/side for {symbol} position.")
        return None

    # 3. Check if there's an actual position to close
    if position_size <= 0 or position_side == 'None':
        print(f"No active position found for {symbol} to close (Size: {position_size}, Side: {position_side}).")
        return None

    # 4. Determine necessary closing side
    closing_order_side = 'Sell' if position_side == 'Buy' else 'Buy'

    # 5. Format the closing quantity (optional: re-fetch instrument info for potentially updated steps)
    instrument_info = get_instrument_info(symbol) # Re-fetch for robustness, though likely unnecessary if position exists
    if not instrument_info:
        print(f"Warning: Could not get instrument info for closing {symbol}. Using default qty step '1'.")
        quantity_step_string = '1'
    else:
        lot_size_filter = instrument_info.get('lotSizeFilter', {})
        quantity_step_string = lot_size_filter.get('qtyStep', '1')

    formatted_quantity = format_quantity(position_size, quantity_step_string)
    if not formatted_quantity or float(formatted_quantity) <= 0:
        print(f"Error: Formatted closing quantity '{formatted_quantity}' is invalid for {symbol}.")
        return None

    # 6. Place the closing market order
    print(f"Attempting to close {position_side} position: {closing_order_side} {formatted_quantity} {symbol} (Original size: {position_size})")
    order_id = create_order(symbol, closing_order_side, 'Market', amount=formatted_quantity)
    if order_id:
        print(f"Market close order placed successfully. Order ID: {order_id}")
    # else: Error handled in create_order

    return order_id

def fetch_and_prepare_data(symbol, timeframe='15m', limit=200):
    """
    Fetches candlestick (kline) data for a given symbol and timeframe, then prepares it as a pandas DataFrame.
    
    Args:
        symbol (str): The trading pair symbol (e.g., 'BTCUSDT').
        timeframe (str): The interval for each candlestick (e.g., '15m' for 15 minutes). 
                         Supported timeframes: '1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '12h', '1d', '1w', '1M'.
        limit (int): The maximum number of candlesticks to fetch (default: 200, max: 1000).
    
    Returns:
        pd.DataFrame: A DataFrame with columns ['open', 'high', 'low', 'close', 'volume'], indexed by timestamp.
                     Returns None if the fetch or processing fails.
    """
    # Step 1: Validate the timeframe input
    # Bybit uses specific codes for timeframes (e.g., '15m' is '15').
    timeframe_map = {
        '1m': '1', '3m': '3', '5m': '5', '15m': '15', '30m': '30',
        '1h': '60', '2h': '120', '4h': '240', '6h': '360', '12h': '720',
        '1d': 'D', '1w': 'W', '1M': 'M'
    }
    interval = timeframe_map.get(timeframe)
    if not interval:
        print(f"Error: Unsupported timeframe '{timeframe}'. Use one of: {list(timeframe_map.keys())}")
        return None

    # Step 2: Validate the limit input
    # Ensure the limit is between 1 and 1000 (Bybit's API constraint).
    try:
        fetch_limit = max(1, min(int(limit), 1000))
    except ValueError:
        print(f"Error: Invalid limit value '{limit}'. Must be an integer.")
        return None

    # Step 3: Fetch the raw candlestick data from Bybit's API
    params = {
        'category': 'linear',  # Trading category (e.g., linear for perpetual contracts)
        'symbol': symbol,      # Trading pair (e.g., 'BTCUSDT')
        'interval': interval,  # Timeframe code (e.g., '15' for 15m)
        'limit': fetch_limit   # Number of candlesticks to fetch
    }
    result = make_request('GET', '/v5/market/kline', params=params)

    # Step 4: Check if the API call succeeded
    if not result or not result.get('list'):
        print(f"Failed to fetch candlestick data for {symbol} {timeframe}.")
        return None

    raw_candles = result['list']
    if not raw_candles:
        print(f"No candlestick data returned for {symbol} {timeframe}.")
        return None

    # Step 5: Process the raw data
    # Bybit returns the newest candle first, so reverse for chronological order.
    raw_candles.reverse()

    # Define the expected columns in the raw data.
    # Renamed 'timestamp_start_ms' to 'timestamp' for consistency with Exchange.py DataFrame structure
    columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover']

    try:
        # Convert the raw data into a pandas DataFrame.
        dataframe = pd.DataFrame(raw_candles, columns=columns)
    except ValueError as e:
        print(f"Error creating DataFrame: {e}. Check if the API response format changed.")
        return None

    # Step 6: Convert the timestamp to a readable datetime format and set it as the index.
    # Explicitly convert to numeric first to avoid FutureWarning
    # Timestamp column is already named 'timestamp' from the columns list above
    dataframe['timestamp'] = pd.to_datetime(pd.to_numeric(dataframe['timestamp']), unit='ms') 
    dataframe.set_index('timestamp', inplace=True)

    # Step 7: Select and rename the essential columns (Open, High, Low, Close, Volume).
    ohlcv_columns = ['open', 'high', 'low', 'close', 'volume']
    dataframe = dataframe[ohlcv_columns]

    # Step 8: Convert all numeric columns to float (handling errors gracefully).
    for col in ohlcv_columns:
        dataframe[col] = pd.to_numeric(dataframe[col], errors='coerce')

    # Step 9: Drop rows with missing or invalid data (e.g., NaN values).
    dataframe.dropna(subset=['open', 'high', 'low', 'close'], inplace=True)

    # Step 10: Ensure there's enough data for analysis (at least 2 candlesticks).
    if len(dataframe) < 2:
        print(f"Warning: Not enough valid data points ({len(dataframe)}) for {symbol} {timeframe}.")
        return None

    return dataframe

# --- Example Usage ---
if __name__ == "__main__":
    print("--- Starting Exchange Demo Script ---")
    # Fetch Price Example
    print(get_current_price('BTCUSDT'))
    fetch_and_prepare_data('BTCUSDT')
    print_balance()
    open_position('BTCUSDT', 'buy', 130)
    print(get_position_pnl('BTCUSDT'))
    # print_balance()
    # close_all_positions()

    print("\n--- Exchange Demo Script Finished ---")
    