# Trading Bot Configuration

# Exchange parameters
exchange_config = {
    'wallet_address': '******************************************',
    'secret': '0x2f709b0634433678c7c358b73abcdf69d6b15b72bf1d22fb54aeec512a0323a6',
    'trade_amount_usdc': 50
}

class Config:
    # Define your configuration settings here
    pass

# Path to the directory containing CSV data files
CSV_DATA_DIR = 'project/data' # Adjusted to be relative to the workspace root
