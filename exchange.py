import time
import ccxt
import pandas as pd
from config.config import exchange_config

# Initialize exchange client
hyperliquid = ccxt.hyperliquid({
    'walletAddress': exchange_config.get('wallet_address'),
    'privateKey': exchange_config.get('secret'),
})

def get_current_price(symbol):
    """Fetch current market price for a symbol."""
    try:
        ticker = hyperliquid.fetch_ticker(symbol)
        mid_px_value = ticker['info']['midPx']
        if mid_px_value is not None and mid_px_value != '':
            return float(mid_px_value)
        else:
            print(f"Warning: 'midPx' is None or empty in ticker info for {symbol}. Ticker: {ticker}")
            return None
    except Exception as e:
        print(f"Error fetching or processing ticker for current price {symbol}: {e}")
        return None

def get_balance():
    """Fetch account balance and margin details."""
    try:
        balance = hyperliquid.fetch_balance()
        usdc = balance['USDC']
        margin_used = float(balance['info']['marginSummary'].get('totalMarginUsed', '0'))
        return {
            'total': usdc['total'],
            'free': usdc['total'] - margin_used,
            'used': margin_used
        }
    except Exception:
        return None

def convert_usdc_to_contracts(amount, symbol, price=None):
    """Convert USDC amount to contracts using current price or a provided price."""
    if price is None:
        price = get_current_price(symbol)

    if price and price > 0:
        return amount / price
    else:
        print(f"Cannot convert USDC to contracts for {symbol}: Invalid or zero price ({price})")
        return None

def get_position(symbol):
    """Fetch current position for a symbol."""
    try:
        positions = hyperliquid.fetch_positions([symbol])
        return positions[0] if positions else None
    except Exception:
        return None

def get_all_positions():
    """Fetch all open positions."""
    try:
        return hyperliquid.fetch_positions()
    except Exception:
        return []

def get_position_pnl(symbol):
    """Get PnL for current position directly from exchange data."""
    position = get_position(symbol)
    if not position:
        return None

    try:
        pnl = position['unrealizedPnl']
        return float(pnl) if pnl is not None else None
    except (KeyError, ValueError, TypeError):
        return None

def create_order(symbol, side, amount, price):
    """
    Places a single limit order.
    Returns the order details or None on failure.
    """
    if not price or amount <= 0:
        print(f"Invalid parameters for creating order: price={price}, amount={amount}")
        return None

    try:
        print(f"Placing limit order: {symbol}, {side}, {amount}, {price}")
        order = hyperliquid.create_order(symbol, 'limit', side, amount, price)
        if order and 'id' in order:
            print(f"Successfully placed order ID: {order['id']}")
            return order
        else:
            print(f"Failed to place order or received invalid response: {order}")
            return None
    except Exception as e:
        print(f"Exception placing order: {e}")
        return None

def open_position(symbol, side, usdc_amount, max_retries=5, retry_delay=5, check_interval=1):
    """Open position with retry logic, polling for fills."""
    for attempt in range(max_retries):
        try:
            print(f"Attempt {attempt + 1}/{max_retries} to open {side} position for {symbol}...")
            ticker = hyperliquid.fetch_ticker(symbol)
            if not ticker or ticker.get('ask') is None or ticker.get('bid') is None:
                print(f"Could not fetch ticker or bid/ask is missing/None for {symbol}. Retrying...")
                time.sleep(retry_delay) # Still wait before full retry
                continue

            try:
                exec_price = float(ticker['ask']) if side == 'buy' else float(ticker['bid'])
                if exec_price <= 0:
                    raise ValueError("Execution price must be positive")
            except (ValueError, TypeError) as price_err:
                print(f"Invalid bid/ask price ({ticker.get('bid')}, {ticker.get('ask')}) for {symbol}: {price_err}. Retrying...")
                time.sleep(retry_delay)
                continue

            contracts = convert_usdc_to_contracts(usdc_amount, symbol, price=exec_price)
            if not contracts or contracts <= 0:
                print(f"Could not convert {usdc_amount} USDC to contracts for {symbol} at price {exec_price}. Retrying...")
                time.sleep(retry_delay)
                continue

            order = create_order(symbol, side, contracts, exec_price)
            if not order or 'id' not in order:
                print(f"Failed to create order for {symbol}. Retrying...")
                time.sleep(retry_delay) # Wait before retrying placement
                continue

            order_id = order['id']
            print(f"  Order {order_id} placed at price {exec_price:.4f}. Polling for fill ({retry_delay}s max)...")

            order_filled = False
            start_poll_time = time.time()
            while time.time() - start_poll_time < retry_delay:
                order_status = fetch_order_status(order_id, symbol)
                print(f"  Polling... Order {order_id} status: {order_status} (Elapsed: {time.time() - start_poll_time:.1f}s)")
                if order_status == 'closed':
                    print(f"✅ Successfully opened {side} position for {symbol} (Order {order_id}).")
                    order_filled = True
                    return True # Exit function on success
                elif order_status not in ['open', 'new']: # Consider 'new' as potentially open
                    # Handle unexpected statuses like canceled, rejected, expired during polling
                    print(f"  Order {order_id} reached unexpected status '{order_status}' during polling. Stopping poll for this attempt.")
                    break # Exit polling loop, will likely lead to cancel/retry
                
                # Wait before next check, but ensure total time doesn't exceed retry_delay significantly
                wait_time = min(check_interval, retry_delay - (time.time() - start_poll_time))
                if wait_time > 0:
                    time.sleep(wait_time)

            # --- If loop finishes without the order being filled ---            
            if not order_filled:
                # Double check status one last time after the loop finishes
                final_status = fetch_order_status(order_id, symbol)
                print(f"  Polling finished. Final status for order {order_id}: {final_status}")
                if final_status == 'closed':
                    print(f"✅ Successfully opened {side} position for {symbol} (Order {order_id}) - detected on final check.")
                    return True
                elif final_status == 'open':
                    print(f"  Order {order_id} still open after {retry_delay}s. Cancelling...")
                    try:
                        cancel_result = hyperliquid.cancel_order(order_id, symbol)
                        print(f"  Order {order_id} cancellation result: {cancel_result}")
                    except Exception as cancel_e:
                        print(f"  Could not cancel order {order_id}: {cancel_e}")
                else:
                    print(f"  Order {order_id} has status '{final_status}' after polling. Proceeding to retry placement...")
                    # No cancellation needed if it's already rejected/cancelled/etc.
       
        except ccxt.NetworkError as ne:
            print(f"Network error during open_position attempt {attempt + 1}: {ne}. Retrying...")
            time.sleep(retry_delay)
        except ccxt.ExchangeError as ee:
            print(f"Exchange error during open_position attempt {attempt + 1}: {ee}. Retrying...")
            time.sleep(retry_delay)
        except Exception as e:
            print(f"Unexpected exception during open_position attempt {attempt + 1}: {e} ({type(e).__name__})")
            if attempt < max_retries - 1:
                print(f"Waiting {retry_delay}s before next attempt...")
                time.sleep(retry_delay)

    print(f"❌ Failed to open {side} position for {symbol} after {max_retries} attempts.")
    return False

def close_position(symbol, max_retries=5, retry_delay=5, check_interval=1):
    """Close position with retry logic, polling for fills."""
    for attempt in range(max_retries):
        try:
            print(f"Attempt {attempt + 1}/{max_retries} to close position for {symbol}...")
            position = get_position(symbol)
            if not position or position.get('contracts') is None:
                print(f"No position or contracts info found for {symbol}. Assuming closed.")
                return True

            try:
                contracts = abs(float(position['contracts']))
            except (ValueError, TypeError):
                print(f"Invalid position contracts value ({position.get('contracts')}) for {symbol}. Cannot close.")
                return False

            if contracts <= 0:
                print(f"Position size is zero for {symbol}. Assuming closed.")
                return True

            side = 'sell' if position.get('side') == 'long' else 'buy'

            ticker = hyperliquid.fetch_ticker(symbol)
            if not ticker or ticker.get('ask') is None or ticker.get('bid') is None:
                print(f"Could not fetch ticker or bid/ask for {symbol}. Retrying...")
                time.sleep(retry_delay)
                continue

            try:
                exec_price = float(ticker['bid']) if side == 'sell' else float(ticker['ask'])
                if exec_price <= 0:
                    raise ValueError("Execution price must be positive")
            except (ValueError, TypeError) as price_err:
                print(f"Invalid bid/ask price ({ticker.get('bid')}, {ticker.get('ask')}) for {symbol}: {price_err}. Retrying...")
                time.sleep(retry_delay)
                continue

            order = create_order(symbol, side, contracts, exec_price)
            if not order or 'id' not in order:
                print(f"Failed to create closing order for {symbol}. Retrying...")
                time.sleep(retry_delay)
                continue

            order_id = order['id']
            print(f"  Closing order {order_id} placed at price {exec_price:.4f}. Polling for fill ({retry_delay}s max)...")

            order_filled = False
            start_poll_time = time.time()
            while time.time() - start_poll_time < retry_delay:
                order_status = fetch_order_status(order_id, symbol)
                print(f"  Polling... Order {order_id} status: {order_status} (Elapsed: {time.time() - start_poll_time:.1f}s)")
                if order_status == 'closed':
                    print(f"✅ Successfully closed position for {symbol} (Order {order_id}).")
                    order_filled = True
                    return True # Exit function on success
                elif order_status not in ['open', 'new']:
                    print(f"  Order {order_id} reached unexpected status '{order_status}' during polling. Stopping poll.")
                    break
                
                wait_time = min(check_interval, retry_delay - (time.time() - start_poll_time))
                if wait_time > 0:
                    time.sleep(wait_time)

            # --- If loop finishes without the order being filled ---
            if not order_filled:
                final_status = fetch_order_status(order_id, symbol)
                print(f"  Polling finished. Final status for order {order_id}: {final_status}")
                if final_status == 'closed':
                    print(f"✅ Successfully closed position for {symbol} (Order {order_id}) - detected on final check.")
                    return True
                elif final_status == 'open':
                    print(f"  Closing order {order_id} still open after {retry_delay}s. Cancelling...")
                    try:
                        cancel_result = hyperliquid.cancel_order(order_id, symbol)
                        print(f"  Order {order_id} cancellation result: {cancel_result}")
                    except Exception as cancel_e:
                        print(f"  Could not cancel order {order_id}: {cancel_e}")
                else:
                    print(f"  Order {order_id} has status '{final_status}' after polling. Proceeding to retry placement...")

        except ccxt.NetworkError as ne:
            print(f"Network error during close_position attempt {attempt + 1}: {ne}. Retrying...")
            time.sleep(retry_delay)
        except ccxt.ExchangeError as ee:
            print(f"Exchange error during close_position attempt {attempt + 1}: {ee}. Retrying...")
            time.sleep(retry_delay)
        except Exception as e:
            print(f"Unexpected exception during close_position attempt {attempt + 1}: {e} ({type(e).__name__})")
            if attempt < max_retries - 1:
                print(f"Waiting {retry_delay}s before next attempt...")
                time.sleep(retry_delay)

    print(f"❌ Failed to close position for {symbol} after {max_retries} attempts.")
    return False

def close_all_positions():
    """Close all open positions."""
    results = {}
    for pos in get_all_positions():
        results[pos['symbol']] = close_position(pos['symbol'])
    return results

def fetch_and_prepare_data(symbol, timeframe='15m', days=None):
    """
    Fetch historical OHLCV data from the exchange.

    Args:
        symbol: Trading pair symbol (e.g., 'BTC/USDC:USDC')
        timeframe: Candle timeframe (e.g., '15m')
        days: Number of days of historical data to fetch (None uses exchange default limit)

    Returns:
        DataFrame with OHLCV data (index=timestamp) or None on error.
    """
    try:
        limit = None
        if days is not None and days > 0:
            tf_in_minutes = hyperliquid.parse_timeframe(timeframe) // 60
            if tf_in_minutes > 0:
                candles_per_day = 1440 // tf_in_minutes
                limit = min(candles_per_day * days, 5000) # Use min to cap at exchange limit

        candles = hyperliquid.fetch_ohlcv(symbol=symbol, timeframe=timeframe, limit=limit)
        if not candles or len(candles) < 2:
            return None

        # Convert to dataframe
        df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True) # Ensure chronological order

        # Convert OHLCV columns to numeric, coercing errors
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        df.dropna(subset=['open', 'high', 'low', 'close'], inplace=True)

        if len(df) < 2:
            return None
        return df
    except Exception as e:
        print(f"Error fetching and preparing data for {symbol}: {e}")
        return None

def cancel_order(order_id, symbol):
    """Cancel an order."""
    try:
        return hyperliquid.cancel_order(order_id, symbol)
    except Exception:
        return None

def fetch_order_status(order_id, symbol):
    """Check if an order is filled."""
    try:
        order = hyperliquid.fetch_order(order_id, symbol)
        return order.get('status', 'unknown')
    except Exception:
        return 'error'

# Example usage
if __name__ == "__main__":
    print([market['symbol'] for market in (hyperliquid.fetch_markets() or [])])