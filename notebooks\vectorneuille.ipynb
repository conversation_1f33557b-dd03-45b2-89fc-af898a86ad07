{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas_ta as ta # utilisé via df.ta\n", "import vectorbt as vbt\n", "import numpy as np\n", "\n", "from exchange import fetch_and_prepare_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== CONFIGURATION =====\n", "# Mean reversion strategy parameters\n", "BBANDS_PERIOD = 20         # Period for Bollinger Bands\n", "BBANDS_STD = 2.0           # Standard deviation for Bollinger Bands\n", "ADX_PERIOD = 14            # Period for ADX calculation\n", "ADX_THRESHOLD = 20         # ADX threshold for trend strength\n", "ADX_THRESHOLD_FILTER = 60  # Filter to avoid trading in directional markets\n", "SMA_PERIOD = 200           # Period for SMA calculation\n", "SMA_BOUNCE_THRESHOLD = 0.002  # Threshold for price bounce from SMA (0.2%)\n", "INITIAL_ENTRY_SIZE = 0.01  # Initial position size (1% of portfolio)\n", "DCA_SIZE_INCREMENT = 0.01  # Increment for each DCA (1% additional)\n", "MAX_DCA_SIZE = 0.10  # Maximum DCA size (10% of portfolio)\n", "EXIT_SIZE = 1.0            # Exit size (100% of position)\n", "TF = '1h'\n", "# Portfolio settings\n", "INITIAL_CASH = 500000\n", "FEE = 0.0004"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                         open      high       low     close      volume\n", "timestamp                                                              \n", "2024-12-16 09:00:00  104641.0  105062.0  104600.0  104740.0   380.78801\n", "2024-12-16 10:00:00  104741.0  104800.0  104400.0  104767.0   284.75954\n", "2024-12-16 11:00:00  104767.0  104923.0  103674.0  103810.0   376.88095\n", "2024-12-16 12:00:00  103810.0  104221.0  103396.0  103728.0   628.30761\n", "2024-12-16 13:00:00  103728.0  104270.0  103550.0  104011.0   721.35448\n", "...                       ...       ...       ...       ...         ...\n", "2025-07-12 13:00:00  117559.0  117607.0  117260.0  117544.0   796.95235\n", "2025-07-12 14:00:00  117543.0  117787.0  117334.0  117423.0   712.85155\n", "2025-07-12 15:00:00  117423.0  117506.0  116964.0  117148.0  1110.35324\n", "2025-07-12 16:00:00  117149.0  117438.0  117098.0  117229.0   392.47116\n", "2025-07-12 17:00:00  117230.0  117460.0  117220.0  117450.0   229.00419\n", "\n", "[5001 rows x 5 columns]\n"]}], "source": ["# ===== DATA PREPARATION =====\n", "symbol = \"BTC/USDC:USDC\"\n", "data = fetch_and_prepare_data(symbol, TF)\n", "# data = pd.read_csv(\"data/BTCUSD_1h_Combined_Index.csv\").iloc[:,:5][90000:]\n", "\n", "data.columns = [c.lower() for c in data.columns]\n", "\n", "print(data)\n", "\n", "required_cols = ['close', 'low', 'high']\n", "if not all(col in data.columns for col in required_cols):\n", "    raise ValueError(f\"Missing required columns. Got: {data.columns.tolist()}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# ===== TECHNICAL INDICATORS =====\n", "# Add indicators using pandas_ta\n", "# 1. Bollinger Bands\n", "data.ta.bbands(length=BBANDS_PERIOD, std=BBANDS_STD, append=True)\n", "\n", "# 2. ADX - Average Directional Index for trend strength\n", "data.ta.adx(length=ADX_PERIOD, append=True)\n", "\n", "# 3. SMA 200 - Support/Resistance level\n", "data.ta.sma(length=SMA_PERIOD, append=True)\n", "\n", "# Clean up NaN values\n", "data.dropna(inplace=True)\n", "data.reset_index(drop=True, inplace=True)  # Reset index after dropping NaNs\n", "\n", "# Define column names for readability\n", "bbl_col = f'BBL_{BBANDS_PERIOD}_{BBANDS_STD}'\n", "bbm_col = f'BBM_{BBANDS_PERIOD}_{BBANDS_STD}'\n", "bbu_col = f'BBU_{BBANDS_PERIOD}_{BBANDS_STD}'\n", "adx_col = f'ADX_{ADX_PERIOD}'\n", "sma_col = f'SMA_{SMA_PERIOD}'\n", "\n", "print(data)\n", "\n", "# Print sample of calculated indicators\n", "print(\"\\n===== Technical Indicators =====\")\n", "indicator_cols = [bbl_col, bbm_col, bbu_col, adx_col, sma_col]\n", "print(data[['close'] + indicator_cols].tail(20))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== ENTRY/EXIT SIGNALS =====\n", "# --- LONG & SHORT STRATEGIES ---\n", "# Trend condition for adaptive exits\n", "weak_trend = data[adx_col] < ADX_THRESHOLD\n", "strong_trend = ~weak_trend  # Opposite of weak trend\n", "# Filter condition for high ADX - avoid trading in strongly directional markets\n", "high_adx_filter = data[adx_col] >= ADX_THRESHOLD_FILTER\n", "\n", "# Define entry conditions\n", "# Initial entries: Price crosses from below to above the lower band for longs\n", "# AND ADX is below the filter threshold\n", "long_initial_entries = (data['close'].shift(1) < data[bbl_col].shift(1)) & (data['close'] >= data[bbl_col]) & (~high_adx_filter)\n", "\n", "# DCA conditions for longs: Price touches or goes below lower band\n", "# AND ADX is below the filter threshold\n", "long_dca_conditions = (data['low'] <= data[bbl_col]) & (~high_adx_filter)\n", "\n", "# Initial entries: Price crosses from above to below the upper band for shorts\n", "# AND ADX is below the filter threshold\n", "short_initial_entries = (data['close'].shift(1) > data[bbu_col].shift(1)) & (data['close'] <= data[bbu_col]) & (~high_adx_filter)\n", "\n", "# DCA conditions for shorts: Price touches or goes above upper band\n", "# AND ADX is below the filter threshold\n", "short_dca_conditions = (data['high'] >= data[bbu_col]) & (~high_adx_filter)\n", "\n", "# Initialize arrays for position tracking\n", "long_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position\n", "short_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position\n", "\n", "# Initialize signal arrays\n", "long_entries = pd.Series(False, index=data.index)\n", "short_entries = pd.Series(False, index=data.index)\n", "long_exits = pd.Series(False, index=data.index)\n", "short_exits = pd.Series(False, index=data.index)\n", "\n", "# Track positions, DCA opportunities, and generate clean signals in a single pass\n", "for i in range(len(data)):\n", "    # Carry forward position state from previous bar (if not first bar)\n", "    if i > 0:\n", "        long_position.iloc[i] = long_position.iloc[i-1]\n", "        short_position.iloc[i] = short_position.iloc[i-1]\n", "    \n", "    # Define exit conditions - UPDATED to exit on:\n", "    # 1. <PERSON> reaching the opposite band\n", "    # 2. Price reaching middle line if ADX is low (weak trend)\n", "    # 3. ADX exceeding the filter threshold (strong directional market)\n", "    long_exit_condition = (data['close'].iloc[i] >= data[bbu_col].iloc[i]) | \\\n", "                          ((data['close'].iloc[i] >= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \\\n", "                          high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold\n", "    \n", "    short_exit_condition = (data['close'].iloc[i] <= data[bbl_col].iloc[i]) | \\\n", "                           ((data['close'].iloc[i] <= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \\\n", "                           high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold\n", "    \n", "    # Process positions in priority order: exits first, then entries\n", "    \n", "    # Process exits (only if we have a position)\n", "    if long_position.iloc[i] > 0 and long_exit_condition:\n", "        long_exits.iloc[i] = True\n", "        long_position.iloc[i] = 0  # Close position\n", "    \n", "    if short_position.iloc[i] > 0 and short_exit_condition:\n", "        short_exits.iloc[i] = True\n", "        short_position.iloc[i] = 0  # Close position\n", "    \n", "    # Process initial entries (only if we don't have a position)\n", "    if long_position.iloc[i] == 0 and long_initial_entries.iloc[i]:\n", "        long_entries.iloc[i] = True\n", "        long_position.iloc[i] = 1  # Open position\n", "    \n", "    if short_position.iloc[i] == 0 and short_initial_entries.iloc[i]:\n", "        short_entries.iloc[i] = True\n", "        short_position.iloc[i] = 1  # Open position\n", "    \n", "    # Process DCA opportunities (only if we already have a position and no exit signal on same bar)\n", "    if long_position.iloc[i] > 0 and not long_exits.iloc[i] and long_dca_conditions.iloc[i]:\n", "        # Only add DCA if we didn't already have an entry on this bar\n", "        if not long_entries.iloc[i]:\n", "            long_entries.iloc[i] = True  # Add to position\n", "    \n", "    if short_position.iloc[i] > 0 and not short_exits.iloc[i] and short_dca_conditions.iloc[i]:\n", "        # Only add DCA if we didn't already have an entry on this bar\n", "        if not short_entries.iloc[i]:\n", "            short_entries.iloc[i] = True  # Add to position\n", "\n", "# Count entries and exits by type\n", "long_initial_count = sum(long_initial_entries & long_entries)\n", "long_dca_count = sum(long_entries) - long_initial_count\n", "short_initial_count = sum(short_initial_entries & short_entries)\n", "short_dca_count = sum(short_entries) - short_initial_count\n", "\n", "\n", "# Create simplified entry tracking\n", "entry_counts = pd.DataFrame({\n", "    'Count': [\n", "        sum(long_entries),\n", "        sum(short_entries)\n", "    ]\n", "}, index=['Long', 'Short'])\n", "\n", "# Plot distribution of long vs short entries\n", "# entry_counts.plot.pie(y='Count', autopct='%1.1f%%')\n", " \n", "# Print signal statistics\n", "print(\"\\n===== Signal Statistics =====\")\n", "print(f\"Long entries: {long_initial_count}\")\n", "print(f\"Long exits: {sum(long_exits)}\")\n", "print(f\"Short entries: {short_initial_count}\")\n", "print(f\"Short exits: {sum(short_exits)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== CREATE VARIABLE SIZING =====\n", "# Create arrays for position sizing that vary between entries and exits\n", "# For initial entries: use INITIAL_ENTRY_SIZE (1%)\n", "# For DCA entries: start with INITIAL_ENTRY_SIZE (1%) and increment by DCA_SIZE_INCREMENT (1%) \n", "# until reaching MAX_DCA_SIZE (10%)\n", "# For exits: use EXIT_SIZE (100%)\n", "size_array = pd.Series(INITIAL_ENTRY_SIZE, index=data.index)\n", "\n", "# Track DCA counts for long and short positions to increment sizing\n", "long_dca_count = 0\n", "short_dca_count = 0\n", "long_in_position = False\n", "short_in_position = False\n", "\n", "# Create arrays to track portfolio usage\n", "portfolio_pct_used = pd.Series(0.0, index=data.index)  # Track total portfolio % used\n", "long_pct_used = pd.Series(0.0, index=data.index)      # Track % used for long positions\n", "short_pct_used = pd.Series(0.0, index=data.index)     # Track % used for short positions\n", "\n", "# Process each bar to set appropriate sizing\n", "for i in range(len(data)):\n", "    # Carry forward usage values from previous bar (if not first bar)\n", "    if i > 0:\n", "        long_pct_used.iloc[i] = long_pct_used.iloc[i-1]\n", "        short_pct_used.iloc[i] = short_pct_used.iloc[i-1]\n", "        portfolio_pct_used.iloc[i] = long_pct_used.iloc[i] + short_pct_used.iloc[i]\n", "    \n", "    # Check for exits first (reset DCA counters and portfolio usage)\n", "    if long_exits.iloc[i]:\n", "        size_array.iloc[i] = EXIT_SIZE\n", "        long_dca_count = 0\n", "        long_in_position = False\n", "        long_pct_used.iloc[i] = 0.0  # Reset long usage on exit\n", "    \n", "    elif short_exits.iloc[i]:\n", "        size_array.iloc[i] = EXIT_SIZE\n", "        short_dca_count = 0\n", "        short_in_position = False\n", "        short_pct_used.iloc[i] = 0.0  # Reset short usage on exit\n", "    \n", "    # Check for entries\n", "    elif long_entries.iloc[i]:\n", "        if not long_in_position:  # Initial entry\n", "            size_array.iloc[i] = INITIAL_ENTRY_SIZE\n", "            long_in_position = True\n", "            long_pct_used.iloc[i] = INITIAL_ENTRY_SIZE  # Initial usage\n", "        else:  # DCA entry\n", "            long_dca_count += 1\n", "            # Calculate size, but cap at MAX_DCA_SIZE\n", "            dca_size = min(INITIAL_ENTRY_SIZE + (DCA_SIZE_INCREMENT * long_dca_count), MAX_DCA_SIZE)\n", "            size_array.iloc[i] = dca_size\n", "            long_pct_used.iloc[i] += dca_size  # Add to usage\n", "    \n", "    elif short_entries.iloc[i]:\n", "        if not short_in_position:  # Initial entry\n", "            size_array.iloc[i] = INITIAL_ENTRY_SIZE\n", "            short_in_position = True\n", "            short_pct_used.iloc[i] = INITIAL_ENTRY_SIZE  # Initial usage\n", "        else:  # DCA entry\n", "            short_dca_count += 1\n", "            # Calculate size, but cap at MAX_DCA_SIZE\n", "            dca_size = min(INITIAL_ENTRY_SIZE + (DCA_SIZE_INCREMENT * short_dca_count), MAX_DCA_SIZE)\n", "            size_array.iloc[i] = dca_size\n", "            short_pct_used.iloc[i] += dca_size  # Add to usage\n", "    \n", "    # Update total portfolio usage\n", "    portfolio_pct_used.iloc[i] = long_pct_used.iloc[i] + short_pct_used.iloc[i]\n", "\n", "# Print summary of portfolio usage\n", "print(\"\\n===== Portfolio Usage Statistics =====\")\n", "print(f\"Maximum portfolio percentage used: {portfolio_pct_used.max():.2%}\")\n", "print(f\"Maximum long percentage used: {long_pct_used.max():.2%}\")\n", "print(f\"Maximum short percentage used: {short_pct_used.max():.2%}\")\n", "\n", "# ===== PORTFOLIO CREATION =====\n", "# Create a single portfolio that handles both long and short positions using vectorbt's built-in functionality\n", "portfolio = vbt.Portfolio.from_signals(\n", "    close=data['close'],\n", "    entries=long_entries,                # Long entries\n", "    exits=long_exits,                    # Long exits\n", "    short_entries=short_entries,         # Short entries\n", "    short_exits=short_exits,             # Short exits\n", "    size=size_array,                     # Variable position size based on SMA proximity\n", "    size_type='percent',                 # Size as percentage of portfolio\n", "    init_cash=INITIAL_CASH,\n", "    fees=FEE,\n", "    freq=TF,\n", "    sl_stop=0.04,\n", "    accumulate=True                    # Disable DCA (mean reversion strategy uses single entries)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== STATS =====\n", "print(\"\\n===== Portfolio Stats =====\")\n", "stats = portfolio.stats()\n", "\n", "def calculate_beta(strategy_ret: pd.Series,\n", "                   benchmark_ret: pd.Series) -> float:\n", "    \"\"\"\n", "    Beta = Cov(strategy, benchmark) / Var(benchmark).\n", "\n", "    Les deux séries doivent être déjà exprimées en rendements\n", "    (pourcentage ou log-retours) et indexées sur les mêmes dates.\n", "    \"\"\"\n", "    aligned = pd.concat([strategy_ret, benchmark_ret], axis=1).dropna()\n", "    if aligned.shape[0] < 2:\n", "        return np.nan\n", "    cov = np.cov(aligned.iloc[:, 0], aligned.iloc[:, 1])[0, 1]\n", "    var = np.var(aligned.iloc[:, 1])\n", "    return cov / var if var > 0 else np.nan\n", "\n", "# Calcul du beta et ajout aux stats\n", "strategy_returns = portfolio.returns()\n", "asset_returns = data['close'].pct_change()\n", "beta = calculate_beta(strategy_returns, asset_returns)\n", "\n", "# Création d'une série pandas pour le beta avec le même format que les autres stats\n", "beta_stat = pd.Series(beta, index=['Beta vs Benchmark'])\n", "\n", "stats = pd.concat([stats, beta_stat])\n", "\n", "print(stats)\n", "\n", "# Analyze order execution to see if positions close in a single bar\n", "print(\"\\n===== Portfolio Trade Analysis =====\")\n", "print(f\"Total trades: {len(portfolio.trades)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vbt.settings.set_theme('dark')\n", "fig = portfolio.plot(subplot_settings={'orders': {'close_trace_kwargs': {'visible': False}}})\n", "fig = data.vbt.ohlcv.plot(plot_type='candlestick', fig=fig, show_volume=False, xaxis_rangeslider_visible=False)\n", "fig = data[[bbl_col, bbm_col, bbu_col, sma_col]].rename(\n", "    columns={\n", "        bbl_col: 'Lower BB', \n", "        bbm_col: 'Middle BB', \n", "        bbu_col: 'Upper BB',\n", "        sma_col: 'SMA 200'\n", "    }\n", ").vbt.plot(fig=fig)\n", "\n", "fig.update_layout(width=None, height=800, title_text=f\"Bollinger Bands + SMA 200 Strategy - {symbol}\").show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==== INDIVIDUAL TRADE ANALYSIS ====\n", "\n", "# Plot trade analysis for both long and short positions\n", "portfolio.plot(\n", "    subplots=['trades', 'trade_pnl'],\n", "    subplot_settings={\n", "        'trades': dict(title='Trades Timeline'),\n", "        'trade_pnl': dict(title='Individual Trade P&L')\n", "    }\n", ").update_layout(\n", "    width=None,\n", "    height=None,\n", "    title_text=\"Bollinger Bands Mean Reversion Strategy - Trade Performance Analysis\"\n", ").show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}