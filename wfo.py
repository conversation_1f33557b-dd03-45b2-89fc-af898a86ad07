# -*- coding: utf-8 -*-
"""WFO.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1ul4epCZAcmor-Y0b33kttjQ08YkW6nPy

## Walk Forward Optimization
"""

import vectorbt as vbt
import pandas as pd
import numpy as np
import scipy
import sys
sys.path.append("..")
import pandas_ta as ta
import yfinance as yf
from datetime import datetime, timedelta


vbt.settings['plotting']["layout"]["template"] = "vbt_dark"
vbt.settings['plotting']["layout"]["width"] = None
vbt.settings['plotting']['layout']['height'] = None
vbt.settings['array_wrapper']["freq"] = "1d"
vbt.settings['portfolio']['size_granularity'] = 1
vbt.settings['portfolio']['init_cash'] = 10000

"""## Get our Data"""
# Use major stock symbols instead of FX for yfinance
STOCK_LIST = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'V', 'JNJ']
def get_stock_data(symbols=None, period='6mo'):
    """Get stock data using yfinance"""
    if symbols is None:
        symbols = STOCK_LIST
    
    print(f'Loading stock data for {len(symbols)} symbols using yfinance...')
    
    try:
        # Download data for all symbols
        all_data = yf.download(symbols, period=period, progress=False)
        
        # Use Close prices
        if 'Close' in all_data.columns.levels[0]:
            close_data = all_data['Close']
        else:
            close_data = all_data
            
        # Clean data
        close_data = close_data.dropna()
        
        print(f"Successfully loaded data for {len(close_data.columns)} symbols")
        print(f"Date range: {close_data.index[0]} to {close_data.index[-1]}")
        print(close_data.head())
        
        return close_data
        
    except Exception as e:
        print(f"Error loading data: {e}")
        
# Load real stock data using yfinance
data = get_stock_data()

# Add position sizing function
def get_position_size(data, init_cash=10000, risk=0.02):
    """Calculate position size based on risk management"""
    # Simple position sizing - equal weight across all instruments
    num_instruments = len(data.columns)
    position_size = init_cash * risk / num_instruments
    
    # Create position size array matching data shape
    pos_size = pd.DataFrame(
        position_size, 
        index=data.index, 
        columns=data.columns
    )
    print(pos_size.head(2))
    return pos_size

## Develop Strategy

def sma_cross(close, slow_period, fast_period):
    # Handle numpy array input from vectorbt
    if isinstance(close, np.ndarray):
        # Forward fill NaN values in numpy array
        mask = np.isnan(close)
        if mask.any():
            close = close.copy()
            for i in range(1, len(close)):
                if mask[i]:
                    close[i] = close[i-1]
    else:
        # Handle pandas Series
        if hasattr(close, 'ffill'):
            close = close.ffill().values
        else:
            # Already a numpy array, just use it
            pass

    # Calculate Moving Averages
    slow_ma = ta.sma(close, slow_period)
    fast_ma = ta.sma(close, fast_period)
    
    # Calculate Signals - simple boolean logic
    # Long Entry: fast MA crosses above slow MA
    long_entry = np.zeros_like(close, dtype=bool)
    short_entry = np.zeros_like(close, dtype=bool)
    
    for i in range(1, len(close)):
        if fast_ma[i] > slow_ma[i] and fast_ma[i-1] <= slow_ma[i-1]:
            long_entry[i] = True
        elif fast_ma[i] < slow_ma[i] and fast_ma[i-1] >= slow_ma[i-1]:
            short_entry[i] = True
    
    # Trend
    trend = fast_ma > slow_ma

    return long_entry, short_entry, trend

strat = vbt.IndicatorFactory(
    class_name='SMAStrat',
    short_name='sma_strat',
    input_names=['close'],
    param_names=['slow_period', 'fast_period'],
    output_names=['long_entry', 'short_entry', 'trend']
).from_apply_func(
    sma_cross,
    slow_period=200,
    fast_period=50
)

"""## Calculate Strategy Performance Across Different Parameter Values"""

sma_strat = strat.run(
    data,  # Use data directly since it's already close prices
    slow_period=np.arange(40, 150, 10, dtype=int),
    fast_period=np.arange(10, 35, 5, dtype=int),
    param_product=True
)

pos_size = get_position_size(data, init_cash=10000, risk=0.02)

pf = vbt.Portfolio.from_signals(
    data,
    entries=sma_strat.long_entry,
    short_entries=sma_strat.short_entry,
    size=pos_size,
    size_type='amount'
)

stats_df = pf.stats([
    'total_return',
    'total_trades',
    'win_rate',
    'expectancy'
], agg_func=None)
try:
    best_params = stats_df.groupby(('symbol'))['Expectancy'].idxmax()
    print("Best parameters by symbol:")
    print(best_params)
except KeyError:
    print("Expectancy column not found in stats")
    print("Available columns:", stats_df.columns.tolist())

try:
    # Get first available symbol for plotting
    first_symbol = data.columns[0]
    first_params = (110, 10, first_symbol)
    
    fig = pf.plot(column=first_params, width=1200, height=600)
    if fig is not None:
        fig.write_html('backtest.html', full_html=True)
        print("Backtest plot saved to backtest.html")
    else:
        print("Could not generate plot")
except Exception as e:
    print(f"Plotting error: {e}")

"""## Walk Forward Optimization: The Splitter"""
def get_optimized_split(tf, frac, n):
    # Parameter Estimation
    d = tf/(frac + n*(1 - frac))
    di  = frac*d
    do = (1-frac)*d

    # Mixed Integer, Linear Optimization
    c = [-(1/frac - 1), 1]
    Aeq = [[1, n]]
    Aub = [[-1, 1],
           [(1/frac - 1), -1]]
    beq = [tf]
    bub = [0, 0]
    x0_bounds = (di*0.5, di*1.5)
    x1_bounds = (do*0.5, do*1.5)
    res = scipy.optimize.linprog(
        c, A_eq=Aeq, b_eq=beq, A_ub=Aub, b_ub=bub, bounds=(x0_bounds, x1_bounds),
        integrality=[1, 1],
        method='highs',
        options={"disp": True})

    # Solutions
    di, do = res.x

    # Actual Fraction
    frac_a = di/(do+di)

    return int(di), int(do), frac_a

def wfo_split_func(splits, bounds, index, length_IS=20, length_OOS=30):
    if len(splits) == 0:
        new_split = (slice(0, length_IS), slice(length_IS, length_OOS+length_IS))
    else:
        # Previous split, second set, right bound
        prev_end = bounds[-1][1][1]

        # Split Calculation
        new_split = (
            slice(prev_end-length_IS, prev_end),
            slice(prev_end, prev_end + length_OOS)
        )
    if new_split[-1].stop > len(index):
        return None
    return new_split
d_IS, d_OOS, _ = get_optimized_split(len(data.index), 0.75, 10)
print(d_IS, d_OOS)

def split_func(splits, bounds, index, length_IS=20, length_OOS=30):
    if len(splits) == 0:
        new_split = (slice(0, length_IS), slice(length_IS, length_OOS+length_IS))
    else:
        # Previous split, second set, right bound
        prev_end = bounds[-1][1][1]

        # Split Calculation
        new_split = (
            slice(prev_end-length_IS, prev_end),
            slice(prev_end, prev_end + length_OOS)
        )
    if new_split[-1].stop > len(index):
        return None
    return new_split

splitter = vbt.Splitter.from_split_func(
        data.index,  # Use data.index directly
        split_func,
        split_args=(
            vbt.Rep("splits"),
            vbt.Rep("bounds"),
            vbt.Rep("index"),
        ),
        split_kwargs={
            'length_IS':d_IS,
            'length_OOS':d_OOS
        },
        set_labels=["IS", "OOS"]
)

try:
    fig = splitter.plot()
    if fig is not None:
        fig.show()
        fig.write_html('wfo_splits.html', full_html=False)
        print("WFO splits plot saved to wfo_splits.html")
except Exception as e:
    print(f"Splitter plotting error: {e}")

"""## Strategy Performance: Training Set"""

def perf(data, ind, pos_size, metric='sharpe_ratio'):
    pf = vbt.Portfolio.from_signals(
        data,
        entries=ind.long_entry,
        short_entries=ind.short_entry,
        size=pos_size,
        size_type='amount'
    )
    result = getattr(pf, metric)
    return result

train_perf = splitter.apply(
    perf,
    data,
    sma_strat,
    pos_size,
    metric='sharpe_ratio',
    execute_kwargs=dict(
        show_progress=True,
        clear_cache=50,
        collect_garbage=50
    ),
    set_='IS',
    merge_func='concat'
)


try:
    best = train_perf.groupby(['split', 'symbol']).idxmax()
    best[:] = [(i[1], i[2], i[3]) for i in best]

    optimized_long_entry = []
    optimized_short_entry = []
    
    for i in best.index.get_level_values('split').unique():
        try:
            _opt_long = splitter['OOS'].take(sma_strat.long_entry)[i][best[i]]
            _opt_short = splitter['OOS'].take(sma_strat.short_entry)[i][best[i]]
            
            # Remove parameter levels if they exist
            if hasattr(_opt_long, 'droplevel'):
                _opt_long = _opt_long.droplevel(['sma_strat_slow_period', 'sma_strat_fast_period'], axis=1)
                _opt_short = _opt_short.droplevel(['sma_strat_slow_period', 'sma_strat_fast_period'], axis=1)

            optimized_long_entry.append(_opt_long)
            optimized_short_entry.append(_opt_short)
        except Exception as e:
            print(f"Error processing split {i}: {e}")
            continue

    if optimized_long_entry and optimized_short_entry:
        optimized_long_entry = pd.concat(optimized_long_entry)
        optimized_short_entry = pd.concat(optimized_short_entry)
    else:
        print("No optimized signals generated, using simple strategy")
        # Fallback to simple strategy
        simple_strat = strat.run(data, slow_period=50, fast_period=20)
        optimized_long_entry = simple_strat.long_entry
        optimized_short_entry = simple_strat.short_entry
        
except Exception as e:
    print(f"Optimization error: {e}")
    print("Using simple strategy as fallback")
    simple_strat = strat.run(data, slow_period=50, fast_period=20)
    optimized_long_entry = simple_strat.long_entry
    optimized_short_entry = simple_strat.short_entry

"""## Walk Forward Optimization!"""

pf = vbt.Portfolio.from_signals(
    data,
    entries=optimized_long_entry,
    short_entries=optimized_short_entry,
    size=pos_size,
    size_type='amount',
    group_by=[0]*len(data.columns)
)

try:
    fig = pf.plot()
    if fig is not None:
        fig.show()
        fig.write_html('wfo_results.html', full_html=True)
        print("WFO results plot saved to wfo_results.html")
        
        # Print some performance stats
        print("\nWalk Forward Optimization Results:")
        print(f"Total Return: {pf.total_return():.2%}")
        print(f"Sharpe Ratio: {pf.sh:.2f}")
        print(f"Max Drawdown: {pf.get_drawdawns.max_drawdown():.2%}")
        print(f"Total Trades: {pf.total_trades()}")
    else:
        print("Could not generate final plot")
except Exception as e:
    print(f"Final plotting error: {e}")

