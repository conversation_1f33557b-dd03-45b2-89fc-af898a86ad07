#!/usr/bin/env python3
"""
Core Trading System - Streamlined Implementation
Core functionality without optimization and plotting modules.
"""

import warnings
from typing import Dict, Any

import pandas as pd

from core_components import run_backtest, get_available_strategies as get_strategies_from_config, load_strategy_config
from data_manager import load_data_for_strategy
from analysis_pipeline import get_primary_data, create_strategy
from base import StrategyConfig


# Constants
DEFAULT_SPLIT_RATIO = 0.7
DEFAULT_CONFIG_DIR = 'config'
EXCLUDED_CONFIG_FILES = {'data_sources.yaml', 'global_config.yaml', 'settings.yaml'}
PROGRESS_UPDATE_INTERVAL = 20

# Analysis step names
STEP_OPTIMIZATION = "Parameter Optimization"
STEP_WALKFORWARD = "Walk-Forward Analysis"
STEP_MONTE_CARLO = "Monte Carlo Analysis"
STEP_FULL_BACKTEST = "Full Backtest"
STEP_VISUALIZATION = "Generating Visualizations"

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# ============================================================================
# MAIN TRADING SYSTEM
# ============================================================================

def run_trading_system_analysis(strategy_name: str = 'momentum', time_range: str = None, 
                               end_date: str = None, skip_optimization: bool = False) -> Dict[str, Any]:
    """Run trading system analysis with optimal execution order."""
    # Load strategy and data
    strategy_config = load_strategy_config(strategy_name)
    strategy = create_strategy(strategy_name, strategy_config)
    
    print(f"📊 Loading data with time range: {time_range or 'full dataset'}")
    data = load_data_for_strategy(strategy, time_range, end_date)
    
    results = {}
    
    if skip_optimization:
        # Fast mode: Just backtest with default parameters
        print("\n🚀 Running Backtest (Default Parameters)")
        full_backtest_results = run_full_backtest(data, strategy)
        results['full_backtest'] = full_backtest_results
    else:
        # Full analysis mode: OPTIMAL ORDER
        _, _, primary_data = get_primary_data(data, strategy_config)
        
        # Step 1: Backtest with DEFAULT parameters (for comparison)
        print("\n� SSTEP 1: Backtest with Default Parameters")
        default_strategy = create_strategy(strategy_name, strategy_config)  # Fresh copy with defaults
        default_backtest_results = run_full_backtest(data, default_strategy)
        results['default_backtest'] = default_backtest_results
        
        # Step 2: Optimization (find best parameters)
        print("\n🔧 STEP 2: Parameter Optimization")
        optimization_results = run_optimization(strategy, strategy_config, primary_data)
        results['optimization'] = optimization_results
        
        # Step 3: Backtest with OPTIMIZED parameters
        print("\n🚀 STEP 3: Backtest with Optimized Parameters")
        optimized_backtest_results = run_full_backtest(data, strategy)
        results['full_backtest'] = optimized_backtest_results
        
        # Step 4: Walk-Forward Analysis (test robustness)
        print("\n📈 STEP 4: Walk-Forward Analysis")
        walkforward_results = run_walkforward_analysis(strategy, primary_data)
        results['walkforward'] = walkforward_results
        
        # Step 5: Monte Carlo Analysis (test against randomness)
        print("\n🎲 STEP 5: Monte Carlo Analysis")
        # Get actual return from optimized backtest results for significance testing
        actual_return = None
        if 'full_backtest' in results:
            for _, timeframes in results['full_backtest'].items():
                for _, result in timeframes.items():
                    if 'portfolio' in result:
                        stats = result['portfolio'].stats()
                        actual_return = float(stats.get('Total Return [%]', 0))
                        break
                if actual_return is not None:
                    break
        
        monte_carlo_results = run_monte_carlo_analysis(primary_data, strategy, actual_return)
        results['monte_carlo'] = monte_carlo_results
    
    # Final Step: Generate plots
    print("\n📊 FINAL STEP: Generating Visualizations")
    visualization_results = create_visualizations(results, strategy_name)
    results['visualizations'] = visualization_results
    
    return results


# TradingSystem class removed - use run_trading_system_analysis() directly
    
def run_full_backtest(data: Dict[str, Dict[str, pd.DataFrame]], strategy) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """Run full backtest on all csv and timeframes."""
    print("Running full backtest on all symbols and timeframes...")
    results = {}
    
    for symbol, timeframes in data.items():
        results[symbol] = {}
        
        # For multi-timeframe strategies, run backtest on the primary (entry) timeframe
        # but provide all timeframes to the strategy
        required_tfs = strategy.get_required_timeframes()
        
        if len(required_tfs) > 1:
            # Multi-timeframe strategy - use primary timeframe for backtest
            primary_tf = required_tfs[0]  # Entry timeframe
            
            if primary_tf in timeframes:
                try:
                    # Provide all available timeframes to the strategy
                    tf_data = {}
                    for req_tf in required_tfs:
                        if req_tf in timeframes:
                            tf_data[req_tf] = timeframes[req_tf]
                    
                    signals = strategy.generate_signals(tf_data)
                    print(f"✅ Multi-timeframe signals generated for {symbol}")
                    
                    # Run backtest on primary timeframe
                    primary_data = timeframes[primary_tf]
                    portfolio = run_backtest(primary_data, signals)
                    
                    results[symbol][primary_tf] = {
                        'portfolio': portfolio
                    }
                    
                    # Print portfolio stats
                    stats = portfolio.stats()
                    print(f"\n📊 {symbol} {primary_tf} (Multi-TF) Stats:")
                    print(f"  Total Return: {stats.get('Total Return [%]', 0):.2f}%")
                    print(f"  Sharpe Ratio: {stats.get('Sharpe Ratio', 0):.3f}")
                    print(f"  Max Drawdown: {stats.get('Max Drawdown [%]', 0):.2f}%")
                    print(f"  Win Rate: {stats.get('Win Rate [%]', 0):.2f}%")
                    print(f"  Total Trades: {stats.get('Total Trades', 0)}")                  
                except Exception as e:
                    print(f"⚠️ Multi-timeframe backtest failed for {symbol}: {e}")
                    raise e
            else:
                print(f"⚠️ Primary timeframe {primary_tf} not available for {symbol}")
        else:
            # Single timeframe strategy
            for timeframe, df in timeframes.items():
                try:
                    tf_data = {timeframe: df}
                    signals = strategy.generate_signals(tf_data)
                    print(f"✅ Signals generated for {symbol} {timeframe}")
                    
                    # Run backtest
                    portfolio = run_backtest(df, signals)
                    
                    results[symbol][timeframe] = {
                        'portfolio': portfolio
                    }
                    
                    # Print portfolio stats
                    stats = portfolio.stats()
                    print(f"\n📊 {symbol} {timeframe} Stats:")
                    print(f"  Total Return: {stats.get('Total Return [%]', 0):.2f}%")
                    print(f"  Sharpe Ratio: {stats.get('Sharpe Ratio', 0):.3f}")
                    print(f"  Max Drawdown: {stats.get('Max Drawdown [%]', 0):.2f}%")
                    print(f"  Win Rate: {stats.get('Win Rate [%]', 0):.2f}%")
                    print(f"  Total Trades: {stats.get('Total Trades', 0)}")
                except Exception as e:
                    print(f"⚠️ Metrics calculation failed for {symbol} {timeframe}: {e}")
                    raise e
    return results
    
def run_optimization(strategy, strategy_config: StrategyConfig, data: pd.DataFrame) -> Dict[str, Any]:
    """Run parameter optimization."""
    try:
        from strategies import get_strategy_function
        
        if not strategy_config.optimization_grid:
            print("⚠️ No optimization grid found, using default parameters")
            return {'best_params': strategy.parameters}
        
        # Get the signal function
        signal_func = get_strategy_function(strategy.name)
        param_grid = strategy_config.optimization_grid
        
        print(f"🎯 Optimizing {strategy.name} with {len(param_grid)} parameters")
        
        # Simple grid search implementation
        best_params = strategy.parameters.copy()
        best_score = -999999
        
        # Generate parameter combinations
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        
        from itertools import product
        combinations = list(product(*param_values))
        
        print(f"📊 Testing {len(combinations)} parameter combinations...")
        
        for _, combo in enumerate(combinations[:10]):  # Limit to first 10 for testing
            test_params = strategy.parameters.copy()
            for name, value in zip(param_names, combo):
                test_params[name] = value
            
            # Generate signals with test parameters
            signals = signal_func({strategy.get_required_timeframes()[0]: data}, test_params)
            
            # Quick backtest
            portfolio = run_backtest(data, signals)
            
            # Calculate score (using Sharpe ratio)
            try:
                stats = portfolio.stats()
                score = float(stats.get('Sharpe Ratio', -999))
                
                if score > best_score:
                    best_score = score
                    best_params = test_params.copy()
                    
            except Exception:
                continue
        
        # Update strategy with best parameters
        strategy.parameters.update(best_params)
        print(f"✅ Best parameters found: {best_params}")
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'tested_combinations': len(combinations)
        }
        
    except Exception as e:
        print(f"⚠️ Optimization failed: {e}")
        return {'error': str(e), 'best_params': strategy.parameters}


def run_walkforward_analysis(strategy, data: pd.DataFrame) -> Dict[str, Any]:
    """Run walk-forward analysis with proper train/test metrics."""
    try:
        from strategies import get_strategy_function
        
        print(f"📊 Walk-forward analysis on {len(data)} bars")
        
        # Simple walk-forward: split data into windows
        window_size = len(data) // 4  # 25% windows
        step_size = window_size // 2  # 50% overlap
        
        signal_func = get_strategy_function(strategy.name)
        windows = []
        
        for i in range(0, len(data) - window_size, step_size):
            if len(windows) >= 5:  # Limit to 5 windows for testing
                break
                
            train_end = i + window_size
            test_end = min(train_end + step_size, len(data))
            
            if test_end - train_end < 10:  # Skip if test window too small
                continue
            
            train_data = data.iloc[i:train_end]
            test_data = data.iloc[train_end:test_end]
            
            # Generate signals and run backtests for both periods
            try:
                # Train period
                train_signals = signal_func({strategy.get_required_timeframes()[0]: train_data}, strategy.parameters)
                train_portfolio = run_backtest(train_data, train_signals)
                train_stats = train_portfolio.stats()
                
                # Test period  
                test_signals = signal_func({strategy.get_required_timeframes()[0]: test_data}, strategy.parameters)
                test_portfolio = run_backtest(test_data, test_signals)
                test_stats = test_portfolio.stats()
                
                windows.append({
                    'window': len(windows) + 1,
                    'train_start': train_data.index[0],
                    'train_end': train_data.index[-1],
                    'test_start': test_data.index[0],
                    'test_end': test_data.index[-1],
                    'train_size': len(train_data),
                    'test_size': len(test_data),
                    'train_stats': train_stats,
                    'test_stats': test_stats
                })
                
            except Exception as e:
                print(f"⚠️ Window {len(windows) + 1} failed: {e}")
                continue
        
        return {
            'windows': windows,
            'summary': f"Completed {len(windows)} walk-forward windows"
        }
        
    except Exception as e:
        print(f"⚠️ Walk-forward analysis failed: {e}")
        return {'error': str(e)}


def run_monte_carlo_analysis(data: pd.DataFrame, strategy=None, actual_return: float = None) -> Dict[str, Any]:
    """Run Monte Carlo analysis with statistical significance testing."""
    try:
        import numpy as np
        from scipy import stats
        
        print(f"🎲 Monte Carlo analysis on {len(data)} bars")
        
        # Get actual strategy performance if provided
        if actual_return is not None and strategy is not None:
            try:
                from strategies import get_strategy_function
                signal_func = get_strategy_function(strategy.name)
                signals = signal_func({strategy.get_required_timeframes()[0]: data}, strategy.parameters)
                portfolio = run_backtest(data, signals)
                actual_stats = portfolio.stats()
                actual_return = float(actual_stats.get('Total Return [%]', 0))
            except Exception:
                actual_return = 0.0
        
        # Monte Carlo simulations: shuffle returns
        returns = data['close'].pct_change().dropna()
        num_simulations = 1000  # Increased for better statistical power
        
        simulations = []
        random_returns = []
        
        for i in range(num_simulations):
            # Shuffle returns to break any patterns
            shuffled_returns = np.random.permutation(returns)
            
            # Calculate cumulative return
            cum_return = (1 + shuffled_returns).prod() - 1
            total_return_pct = cum_return * 100
            
            simulations.append({
                'simulation': i + 1,
                'total_return': total_return_pct,
                'volatility': shuffled_returns.std() * np.sqrt(252) * 100
            })
            random_returns.append(total_return_pct)
        
        # Statistical significance testing
        random_returns = np.array(random_returns)
    
        # Calculate percentile of actual return
        if actual_return is not None and not np.isnan(actual_return):
            percentile = stats.percentileofscore(random_returns, actual_return)
            p_value = min(percentile, 100 - percentile) / 100  # Two-tailed test
            is_significant = p_value < 0.05
        else:
            percentile = None
            p_value = None
            is_significant = None
    
        
        # Calculate statistics
        statistics = {
            'mean_return': np.mean(random_returns),
            'std_return': np.std(random_returns),
            'min_return': np.min(random_returns),
            'max_return': np.max(random_returns),
            'percentile_5': np.percentile(random_returns, 5),
            'percentile_95': np.percentile(random_returns, 95),
            'actual_return': actual_return,
            'percentile_rank': percentile,
            'p_value': p_value,
            'is_significant': is_significant
        }
        
        return {
            'simulations': simulations,
            'statistics': statistics,
            'summary': f"Completed {num_simulations} Monte Carlo simulations",
            'significance_test': {
                'actual_return': actual_return,
                'percentile_rank': percentile,
                'p_value': p_value,
                'is_significant': is_significant,
                'interpretation': f"Strategy performance is {'significant' if is_significant else 'not significant'} vs random" if is_significant is not None else "No actual return provided"
            }
        }
        
    except Exception as e:
        print(f"⚠️ Monte Carlo analysis failed: {e}")
        return {'error': str(e)}


def create_visualizations(results: Dict[str, Any], strategy_name: str) -> Dict[str, Any]:
    """Create enhanced visualizations with default vs optimized comparison."""
    try:
        # Collect portfolios for plotting
        portfolios = {}
        default_portfolios = {}
        
        # Get optimized results
        if 'full_backtest' in results:
            for symbol, timeframes in results['full_backtest'].items():
                for timeframe, result in timeframes.items():
                    if 'portfolio' in result:
                        portfolios[f"{symbol}_{timeframe}_optimized"] = result['portfolio']
        
        # Get default results (if available)
        if 'default_backtest' in results:
            for symbol, timeframes in results['default_backtest'].items():
                for timeframe, result in timeframes.items():
                    if 'portfolio' in result:
                        default_portfolios[f"{symbol}_{timeframe}_default"] = result['portfolio']
                        portfolios[f"{symbol}_{timeframe}_default"] = result['portfolio']
        
        if portfolios:
            from plotter import plot_comprehensive_analysis, create_comparison_plot

            # Enhanced plotting with comparison data
            plot_results = plot_comprehensive_analysis(
                portfolios=portfolios,
                strategy_name=strategy_name,
                mc_results=results.get('monte_carlo', {}),
                wf_results=results.get('walkforward', {})
            )

            # Create default vs optimized comparison plot if both exist
            if default_portfolios and len(portfolios) > len(default_portfolios):
                print("📊 Creating Default vs Optimized comparison...")
                comparison_results = create_comparison_plot(results, strategy_name)
                plot_results['comparison'] = comparison_results

            return plot_results
        return {}
    except Exception as e:
        print(f"⚠️ Visualization failed: {e}")
        return {"error": str(e)}





# ============================================================================
# MAIN APPLICATION
# ============================================================================

# Use the function from core_components instead of duplicating
get_available_strategies = get_strategies_from_config

def run_strategy_pipeline(strategy_name: str, time_range: str = None, end_date: str = None, 
                         skip_optimization: bool = False) -> Dict[str, Any]:
    """Run strategy pipeline - simplified."""
    try:
        print(f"\n🚀 Starting {strategy_name} strategy...")
        results = run_trading_system_analysis(strategy_name, time_range, end_date, skip_optimization)
        return {"success": True, "results": results}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"success": False, "error": str(e)}

def main():
    """Main entry point."""
    print("🚀 Trading Strategy Analysis Pipeline")
    print("="*50)

    available_strategies = get_available_strategies()
    
    if not available_strategies:
        print("❌ No strategies found in config directory")
        return

    print("\n📊 Available Strategies:")
    for i, strategy in enumerate(available_strategies, 1):
        print(f"{i}. {strategy}")

    try:
        choice = int(input("\nSelect strategy number: ")) - 1
        if choice < 0 or choice >= len(available_strategies):
            raise IndexError("Invalid choice")
        strategy_name = available_strategies[choice]
    except (ValueError, IndexError):
        print("❌ Invalid selection")
        return

    # Ask for time range preference
    print("\n📅 Time Range Options:")
    print("1. Full dataset (default)")
    print("2. Last 2 years")
    print("3. Last 1 year")
    print("4. Last 6 months")
    print("5. Last 3 months")
    print("6. Custom time range")
    
    time_range = None
    end_date = None
    
    try:
        time_choice = input("\nSelect time range (press Enter for full dataset): ").strip()
        if time_choice == '2':
            time_range = '2y'
        elif time_choice == '3':
            time_range = '1y'
        elif time_choice == '4':
            time_range = '6m'
        elif time_choice == '5':
            time_range = '3m'
        elif time_choice == '6':
            custom_range = input("Enter time range (e.g., '18m', '2y', '90d'): ").strip()
            if custom_range:
                time_range = custom_range
            custom_end = input("Enter end date (YYYY-MM-DD, press Enter for most recent): ").strip()
            if custom_end:
                end_date = custom_end
    except Exception as e:
        print(f"⚠️ Invalid time range input: {e}, using full dataset")

    # Ask for analysis mode
    print("\n⚙️ Analysis Mode:")
    print("1. Full analysis with optimization (default)")
    print("2. Fast mode - skip optimization")
    
    skip_optimization = False
    try:
        mode_choice = input("\nSelect analysis mode (press Enter for full analysis): ").strip()
        if mode_choice == '2':
            skip_optimization = True
    except Exception as e:
        print(f"⚠️ Invalid mode choice: {e}, using full analysis")

    results = run_strategy_pipeline(strategy_name, time_range, end_date, skip_optimization)

    if results["success"]:
        print("\n✅ Strategy pipeline completed successfully!")
    else:
        print(f"\n❌ Strategy pipeline failed: {results['error']}")




def quick_test(strategy_name: str, time_range: str = '3m', fast_mode: bool = True):
    """Quick test function for development and debugging with no plotting.
    
    Args:
        strategy_name: Name of the strategy to test
        time_range: Time range for testing (default: 3m)
        fast_mode: If True, skip optimization (default: True)
    
    Example:
        python trading_system.py --quick vectorbt
        python trading_system.py --quick momentum --full
    """
    print(f"🧪 Quick Test: {strategy_name} strategy")
    print(f"📅 Time range: {time_range}")
    print(f"⚡ Mode: {'Fast (no optimization)' if fast_mode else 'Full analysis'}")
    print("=" * 50)
    
    results = run_strategy_pipeline(strategy_name, time_range, skip_optimization=fast_mode)
    
    if results['success']:
        print(f"\n✅ {strategy_name} strategy test completed!")
        
        # Stats already printed in run_full_backtest
    else:
        print(f"❌ Test failed: {results['error']}")

if __name__ == "__main__":
    import sys
    
    # Simple CLI for quick testing
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        if len(sys.argv) < 3:
            print("Usage: python trading_system.py --quick <strategy_name> [--full]")
            print("Available strategies:", get_available_strategies())
            sys.exit(1)
        
        strategy = sys.argv[2]
        fast_mode = '--full' not in sys.argv
        quick_test(strategy, fast_mode=fast_mode)
    else:
        main()
