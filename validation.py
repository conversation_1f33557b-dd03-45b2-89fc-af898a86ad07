#!/usr/bin/env python3
"""Simple validation functions"""

import pandas as pd
from base import Signals


def validate_ohlc_dataframe(df: pd.DataFrame, name: str = "DataFrame") -> None:
    """Basic OHLC validation."""
    if df is None or df.empty:
        raise ValueError(f"{name} cannot be None or empty")
    
    required_columns = ['open', 'high', 'low', 'close']
    missing = [col for col in required_columns if col not in df.columns]
    if missing:
        raise ValueError(f"{name} missing columns: {missing}")


def validate_signals(signals: Signals, data_index: pd.Index, name: str = "Signals") -> None:
    """Basic signals validation."""
    if signals is None or signals.entries is None:
        raise ValueError(f"{name} cannot be None")
    
    # Check alignment
    common_index = data_index.intersection(signals.entries.index)
    if len(common_index) == 0:
        raise ValueError(f"{name} has no common timestamps with data")
