{"cells": [{"cell_type": "code", "execution_count": 2, "id": "beb74f73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['PURR/USDC', 'HFUN/USDC', 'LICK/USDC', 'MANLET/USDC', 'JEFF/USDC', 'SIX/USDC', 'WAGMI/USDC', 'CAPPY/USDC', 'POINTS/USDC', 'TRUMP/USDC', 'GMEOW/USDC', 'PEPE/USDC', 'XULIAN/USDC', 'RUG/USDC', 'ILIENS/USDC', 'FUCKY/USDC', 'CZ/USDC', 'BAGS/USDC', 'ANSEM/USDC', 'TATE/USDC', 'PUMP/USDC', 'KOBE/USDC', 'HAPPY/USDC', 'SCHIZO/USDC', 'SELL/USDC', 'BIGBEN/USDC', 'CATNIP/USDC', 'HBOOST/USDC', 'SUCKY/USDC', 'GUP/USDC', 'FARMED/USDC', 'GPT/USDC', 'PURRPS/USDC', 'BID/USDC', 'HODL/USDC', 'VEGAS/USDC', 'ASI/USDC', 'VAPOR/USDC', 'PANDA/USDC', 'PILL/USDC', 'ADHD/USDC', 'FUN/USDC', 'LADY/USDC', 'MOG/USDC', 'HPEPE/USDC', 'JEET/USDC', 'DROP/USDC', 'MBAPPE/USDC', 'TEST/USDC', 'RAGE/USDC', 'FRAC/USDC', 'ATEHUN/USDC', 'COZY/USDC', 'ARI/USDC', 'WASH/USDC', 'ANT/USDC', 'NFT/USDC', 'RICH/USDC', 'LORA/USDC', 'CATBAL/USDC', 'TJIF/USDC', 'GUESS/USDC', 'MAXI/USDC', 'NMTD/USDC', 'HPUMP/USDC', 'PIGEON/USDC', 'RISE/USDC', 'CINDY/USDC', 'CHINA/USDC', 'STACK/USDC', 'FRIED/USDC', 'NOCEX/USDC', 'RANK/USDC', 'OMNIX/USDC', 'RIP/USDC', 'G/USDC', 'BOZO/USDC', 'SPH/USDC', 'SHOE/USDC', 'MONAD/USDC', 'HOPE/USDC', 'BUSSY/USDC', 'FATCAT/USDC', 'SHREK/USDC', 'PIP/USDC', 'LQNA/USDC', 'NASDAQ/USDC', 'YEETI/USDC', 'SYLVI/USDC', 'FEIT/USDC', 'FRUDO/USDC', 'VIZN/USDC', 'STRICT/USDC', 'AUTIST/USDC', 'MAGA/USDC', 'HGOD/USDC', 'LIQUID/USDC', 'EARTH/USDC', 'UP/USDC', 'NIGGO/USDC', 'HOP/USDC', 'LUCKY/USDC', 'COPE/USDC', 'HPYH/USDC', 'YAP/USDC', 'HYPE/USDC', 'CHEF/USDC', 'WOW/USDC', 'STEEL/USDC', 'RETARD/USDC', 'MEOW/USDC', 'NEIRO/USDC', 'PEAR/USDC', 'HOLD/USDC', 'MUNCH/USDC', 'BERA/USDC', 'GENESY/USDC', 'BUBZ/USDC', 'PICKL/USDC', 'SHEEP/USDC', 'LAUNCH/USDC', 'FARM/USDC', 'FLASK/USDC', 'VAULT/USDC', 'CAT/USDC', 'HYENA/USDC', 'DEPIN/USDC', 'MON/USDC', 'BEATS/USDC', 'ORA/USDC', 'LIQD/USDC', 'H/USDC', 'STAR/USDC', 'SENT/USDC', 'SOLV/USDC', 'FLY/USDC', 'TIME/USDC', 'SOVRN/USDC', 'HWTR/USDC', 'GOD/USDC', 'UBTC/USDC', 'HEAD/USDC', 'VORTX/USDC', 'DEFIN/USDC', 'JPEG/USDC', 'WHYPI/USDC', 'USDE/USDC', 'UETH/USDC', 'USDXL/USDC', 'FEUSD/USDC', 'QUANT/USDC', 'USOL/USDC', 'RAT/USDC', 'TILT/USDC', 'TREND/USDC', 'BUDDY/USDC', 'PRFI/USDC', 'UFART/USDC', 'FUND/USDC', 'DIABLO/USDC', 'PENIS/USDC', 'USDT0/USDC', 'PEG/USDC', 'USH/USDC', 'COOK/USDC', 'RUB/USDC', 'ANON/USDC', 'FRCT/USDC', 'PERP/USDC', 'PURRO/USDC', 'USR/USDC', 'OTTI/USDC', 'USDHL/USDC', 'XAUT0/USDC', 'HORSY/USDC', 'HPENGU/USDC', 'ISLAND/USDC', 'LICKO/USDC', 'UPUMP/USDC', 'USPYX/USDC', 'STLOOP/USDC', 'TRADE/USDC', 'ANZ/USDC', 'UUUSPX/USDC', 'UBONK/USDC', 'LATINA/USDC', 'APU/USDC', 'WOULD/USDC', 'EX/USDC', 'UMOG/USDC', 'HREKT/USDC', 'BTC/USDC:USDC', 'ETH/USDC:USDC', 'ATOM/USDC:USDC', 'MATIC/USDC:USDC', 'DYDX/USDC:USDC', 'SOL/USDC:USDC', 'AVAX/USDC:USDC', 'BNB/USDC:USDC', 'APE/USDC:USDC', 'OP/USDC:USDC', 'LTC/USDC:USDC', 'ARB/USDC:USDC', 'DOGE/USDC:USDC', 'INJ/USDC:USDC', 'SUI/USDC:USDC', 'kPEPE/USDC:USDC', 'CRV/USDC:USDC', 'LDO/USDC:USDC', 'LINK/USDC:USDC', 'STX/USDC:USDC', 'RNDR/USDC:USDC', 'CFX/USDC:USDC', 'FTM/USDC:USDC', 'GMX/USDC:USDC', 'SNX/USDC:USDC', 'XRP/USDC:USDC', 'BCH/USDC:USDC', 'APT/USDC:USDC', 'AAVE/USDC:USDC', 'COMP/USDC:USDC', 'MKR/USDC:USDC', 'WLD/USDC:USDC', 'FXS/USDC:USDC', 'HPOS/USDC:USDC', 'RLB/USDC:USDC', 'UNIBOT/USDC:USDC', 'YGG/USDC:USDC', 'TRX/USDC:USDC', 'kSHIB/USDC:USDC', 'UNI/USDC:USDC', 'SEI/USDC:USDC', 'RUNE/USDC:USDC', 'OX/USDC:USDC', 'FRIEND/USDC:USDC', 'SHIA/USDC:USDC', 'CYBER/USDC:USDC', 'ZRO/USDC:USDC', 'BLZ/USDC:USDC', 'DOT/USDC:USDC', 'BANANA/USDC:USDC', 'TRB/USDC:USDC', 'FTT/USDC:USDC', 'LOOM/USDC:USDC', 'OGN/USDC:USDC', 'RDNT/USDC:USDC', 'ARK/USDC:USDC', 'BNT/USDC:USDC', 'CANTO/USDC:USDC', 'REQ/USDC:USDC', 'BIGTIME/USDC:USDC', 'KAS/USDC:USDC', 'ORBS/USDC:USDC', 'BLUR/USDC:USDC', 'TIA/USDC:USDC', 'BSV/USDC:USDC', 'ADA/USDC:USDC', 'TON/USDC:USDC', 'MINA/USDC:USDC', 'POLYX/USDC:USDC', 'GAS/USDC:USDC', 'PENDLE/USDC:USDC', 'STG/USDC:USDC', 'FET/USDC:USDC', 'STRAX/USDC:USDC', 'NEAR/USDC:USDC', 'MEME/USDC:USDC', 'ORDI/USDC:USDC', 'BADGER/USDC:USDC', 'NEO/USDC:USDC', 'ZEN/USDC:USDC', 'FIL/USDC:USDC', 'PYTH/USDC:USDC', 'SUSHI/USDC:USDC', 'ILV/USDC:USDC', 'IMX/USDC:USDC', 'kBONK/USDC:USDC', 'GMT/USDC:USDC', 'SUPER/USDC:USDC', 'USTC/USDC:USDC', 'NFTI/USDC:USDC', 'JUP/USDC:USDC', 'kLUNC/USDC:USDC', 'RSR/USDC:USDC', 'GALA/USDC:USDC', 'JTO/USDC:USDC', 'NTRN/USDC:USDC', 'ACE/USDC:USDC', 'MAV/USDC:USDC', 'WIF/USDC:USDC', 'CAKE/USDC:USDC', 'PEOPLE/USDC:USDC', 'ENS/USDC:USDC', 'ETC/USDC:USDC', 'XAI/USDC:USDC', 'MANTA/USDC:USDC', 'UMA/USDC:USDC', 'ONDO/USDC:USDC', 'ALT/USDC:USDC', 'ZETA/USDC:USDC', 'DYM/USDC:USDC', 'MAVIA/USDC:USDC', 'W/USDC:USDC', 'PANDORA/USDC:USDC', 'STRK/USDC:USDC', 'PIXEL/USDC:USDC', 'AI/USDC:USDC', 'TAO/USDC:USDC', 'AR/USDC:USDC', 'MYRO/USDC:USDC', 'kFLOKI/USDC:USDC', 'BOME/USDC:USDC', 'ETHFI/USDC:USDC', 'ENA/USDC:USDC', 'MNT/USDC:USDC', 'TNSR/USDC:USDC', 'SAGA/USDC:USDC', 'MERL/USDC:USDC', 'HBAR/USDC:USDC', 'POPCAT/USDC:USDC', 'OMNI/USDC:USDC', 'EIGEN/USDC:USDC', 'REZ/USDC:USDC', 'NOT/USDC:USDC', 'TURBO/USDC:USDC', 'BRETT/USDC:USDC', 'IO/USDC:USDC', 'ZK/USDC:USDC', 'BLAST/USDC:USDC', 'LISTA/USDC:USDC', 'MEW/USDC:USDC', 'RENDER/USDC:USDC', 'kDOGS/USDC:USDC', 'POL/USDC:USDC', 'CATI/USDC:USDC', 'CELO/USDC:USDC', 'HMSTR/USDC:USDC', 'SCR/USDC:USDC', 'NEIROETH/USDC:USDC', 'kNEIRO/USDC:USDC', 'GOAT/USDC:USDC', 'MOODENG/USDC:USDC', 'GRASS/USDC:USDC', 'PURR/USDC:USDC', 'PNUT/USDC:USDC', 'XLM/USDC:USDC', 'CHILLGUY/USDC:USDC', 'SAND/USDC:USDC', 'IOTA/USDC:USDC', 'ALGO/USDC:USDC', 'HYPE/USDC:USDC', 'ME/USDC:USDC', 'MOVE/USDC:USDC', 'VIRTUAL/USDC:USDC', 'PENGU/USDC:USDC', 'USUAL/USDC:USDC', 'FARTCOIN/USDC:USDC', 'AI16Z/USDC:USDC', 'AIXBT/USDC:USDC', 'ZEREBRO/USDC:USDC', 'BIO/USDC:USDC', 'GRIFFAIN/USDC:USDC', 'SPX/USDC:USDC', 'S/USDC:USDC', 'MORPHO/USDC:USDC', 'TRUMP/USDC:USDC', 'MELANIA/USDC:USDC', 'ANIME/USDC:USDC', 'VINE/USDC:USDC', 'VVV/USDC:USDC', 'JELLY/USDC:USDC', 'BERA/USDC:USDC', 'TST/USDC:USDC', 'LAYER/USDC:USDC', 'IP/USDC:USDC', 'OM/USDC:USDC', 'KAITO/USDC:USDC', 'NIL/USDC:USDC', 'PAXG/USDC:USDC', 'PROMPT/USDC:USDC', 'BABY/USDC:USDC', 'WCT/USDC:USDC', 'HYPER/USDC:USDC', 'ZORA/USDC:USDC', 'INIT/USDC:USDC', 'DOOD/USDC:USDC', 'LAUNCHCOIN/USDC:USDC', 'NXPC/USDC:USDC', 'SOPH/USDC:USDC', 'RESOLV/USDC:USDC', 'SYRUP/USDC:USDC', 'PUMP/USDC:USDC'])\n", "                         open      high       low     close     volume\n", "timestamp                                                             \n", "2025-06-07 18:45:00  105572.0  105624.0  105475.0  105557.0   82.44129\n", "2025-06-07 19:00:00  105557.0  105732.0  105557.0  105681.0  184.67855\n", "2025-06-07 19:15:00  105680.0  105834.0  105674.0  105800.0  146.56545\n", "2025-06-07 19:30:00  105797.0  105801.0  105657.0  105658.0   92.23133\n", "2025-06-07 19:45:00  105657.0  105673.0  105584.0  105672.0   73.46719\n"]}], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "from exchange import hyperliquid, fetch_and_prepare_data\n", "import numpy as np\n", "import pandas as pd\n", "\n", "print(hyperliquid.load_markets().keys())\n", "data = fetch_and_prepare_data(\"BTC/USDC:USDC\")\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": null, "id": "dff44dda", "metadata": {}, "outputs": [], "source": ["\n", "# Split data into train and test sets (80/20 split)\n", "\n", "split = 5\n", "data_splits = {}  # Dictionnaire pour stocker les splits\n", "\n", "for i in range(split):\n", "    data_splits[f'data_split_{i}'] = np.array_split(data, split)[i]\n", "\n", "figs = {}\n", "for col in ['close']:\n", "    plot_data = pd.concat([data_splits[f'data_split_{i}'][col].rename(f\"data_split_{i}_{col}\") for i in range(split)], axis=1)\n", "    figs[col] = plot_data.plot(title=f\"{col} par split\")\n", "\n", "# figs['close'].show()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "0f3fdede", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas_ta\\__init__.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.\n", "  from pkg_resources import get_distribution, DistributionNotFound\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Available subplots: dict_keys(['orders', 'trades', 'trade_pnl', 'asset_flow', 'cash_flow', 'assets', 'cash', 'asset_value', 'value', 'cum_returns', 'drawdowns', 'underwater', 'gross_exposure', 'net_exposure'])\n"]}, {"data": {"text/plain": ["Start                         2025-06-07 18:45:00\n", "End                           2025-07-29 20:30:00\n", "Period                          208 days 08:00:00\n", "Start Value                               10000.0\n", "End Value                             9932.574718\n", "Total Return [%]                        -0.674253\n", "Benchmark Return [%]                    11.093532\n", "Max Gross Exposure [%]                      100.0\n", "Total Fees Paid                         30.446076\n", "Max Drawdown [%]                         9.934108\n", "Max Drawdown Duration            83 days 03:00:00\n", "Total Trades                                    2\n", "Total Closed Trades                             1\n", "Total Open Trades                               1\n", "Open Trade PnL                        -295.447958\n", "Win Rate [%]                                100.0\n", "Best Trade [%]                           2.282507\n", "Worst Trade [%]                          2.282507\n", "Avg Winning Trade [%]                    2.282507\n", "Avg Losing Trade [%]                          NaN\n", "Avg Winning Trade Duration      112 days 05:00:00\n", "Avg Losing Trade Duration                     NaT\n", "Profit Factor                                 inf\n", "Expectancy                             228.022677\n", "Sharpe Ratio                            -0.001383\n", "Calmar Ratio                            -0.118611\n", "Omega Ratio                              0.999954\n", "Sortino Rat<PERSON>                           -0.001954\n", "dtype: object"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import vectorbt as vbt\n", "import pandas_ta as ta\n", "vbt.settings.set_theme('dark')\n", "# vbt.settings['plotting']['layout']['width'] = 1200\n", "# vbt.settings['plotting']['layout']['height'] = 200\n", "\n", "entry = ta.rsi(data['close'], length=14, append=True) < 30\n", "pf = vbt.Portfolio.from_signals(\n", "    close=data[\"close\"],\n", "    entries=entry,\n", "    init_cash=10000,\n", "    fees=0.001,\n", "    slippage=0.001,\n", "    freq=\"1h\",\n", "    tp_stop=0.02,\n", "    )\n", " \n", "# To print available subplots for a VectorBT Portfolio, use:\n", "print(\"Available subplots:\", pf.subplots.keys())\n", "\n", "pf.stats()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "fd9fe18d", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'total_return'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mpf\u001b[49m\u001b[43m.\u001b[49m\u001b[43msubplots\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mtotal_return\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m.plot(\n\u001b[32m      2\u001b[39m     title=\u001b[33m\"\u001b[39m\u001b[33mTotal Return\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      3\u001b[39m     yaxis_title=\u001b[33m\"\u001b[39m\u001b[33mReturn (\u001b[39m\u001b[33m%\u001b[39m\u001b[33m)\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      4\u001b[39m     width=\u001b[32m1200\u001b[39m,\n\u001b[32m      5\u001b[39m     height=\u001b[32m200\u001b[39m\n\u001b[32m      6\u001b[39m ).show()\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'total_return'"]}], "source": ["pf.subplots['trades',{:\n", "                      \n", "                      \n", "                      }].plot(\n", "    title=\"Total Return\",\n", "    yaxis_title=\"Return (%)\",\n", "    width=1200,\n", "    height=200\n", ").show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}