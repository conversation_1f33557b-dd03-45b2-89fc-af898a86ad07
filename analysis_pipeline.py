#!/usr/bin/env python3
"""
Analysis Pipeline - Simplified functional approach.

This module provides strategy creation and analysis orchestration
without complex OOP abstractions.
"""

from typing import Dict, Any, <PERSON>ple
import pandas as pd
from base import StrategyConfig


def create_strategy(strategy_name: str, strategy_config: StrategyConfig):
    """Create strategy function wrapper."""
    from strategies import get_strategy_function
    
    signal_func = get_strategy_function(strategy_name)
    
    # Simple wrapper with required interface
    class Strategy:
        def __init__(self):
            self.name = strategy_name
            self.parameters = strategy_config.parameters.copy()
        
        def generate_signals(self, tf_data):
            return signal_func(tf_data, self.parameters)
        
        def get_required_timeframes(self):
            return self.parameters.get('required_timeframes', ['1h'])
        
        def get_parameter(self, key, default=None):
            return self.parameters.get(key, default)
    
    return Strategy()


def get_primary_data(data: Dict[str, Dict[str, pd.DataFrame]], strategy_config: StrategyConfig) -> Tuple[str, str, pd.DataFrame]:
    """Get primary data for analysis."""
    primary_symbol = list(data.keys())[0]
    primary_timeframe = list(data[primary_symbol].keys())[0]
    primary_data = data[primary_symbol][primary_timeframe]
    return primary_symbol, primary_timeframe, primary_data


# Analysis functions moved to main.py for better organization


def run_optimization(strategy, strategy_config: StrategyConfig, data: pd.DataFrame) -> Dict[str, Any]:
    """Run parameter optimization."""
    try:
        from strategies import get_strategy_function
        
        # Get the signal function
        signal_func = get_strategy_function(strategy.name)
        param_grid = strategy_config.optimization_grid
        
        print(f"🎯 Optimizing {strategy.name} with {len(param_grid)} parameters")
        
        # Simple grid search implementation
        best_params = strategy.parameters.copy()
        best_score = -999999
        
        # Generate parameter combinations
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        
        from itertools import product
        combinations = list(product(*param_values))
        
        print(f"📊 Testing {len(combinations)} parameter combinations...")
        
        for i, combo in enumerate(combinations[:10]):  # Limit to first 10 for testing
            test_params = strategy.parameters.copy()
            for name, value in zip(param_names, combo):
                test_params[name] = value
            
            # Generate signals with test parameters
            signals = signal_func({strategy.get_required_timeframes()[0]: data}, test_params)
            
            # Quick backtest
            from core_components import run_backtest
            portfolio = run_backtest(data, signals)
            
            # Calculate score (using Sharpe ratio)
            try:
                stats = portfolio.stats()
                score = float(stats.get('Sharpe Ratio', -999))
                
                if score > best_score:
                    best_score = score
                    best_params = test_params.copy()
                    
            except Exception:
                continue
        
        # Update strategy with best parameters
        strategy.parameters.update(best_params)
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'tested_combinations': len(combinations)
        }
        
    except Exception as e:
        print(f"⚠️ Optimization failed: {e}")
        return {'error': str(e), 'best_params': strategy.parameters}


def run_walkforward_analysis(strategy, data: pd.DataFrame) -> Dict[str, Any]:
    """Run walk-forward analysis."""
    try:
        print(f"📊 Walk-forward analysis on {len(data)} bars")
        
        # Simple walk-forward: split data into windows
        window_size = len(data) // 4  # 25% windows
        step_size = window_size // 2  # 50% overlap
        
        windows = []
        for i in range(0, len(data) - window_size, step_size):
            if len(windows) >= 5:  # Limit to 5 windows for testing
                break
                
            train_end = i + window_size
            test_end = min(train_end + step_size, len(data))
            
            if test_end - train_end < 10:  # Skip if test window too small
                continue
            
            train_data = data.iloc[i:train_end]
            test_data = data.iloc[train_end:test_end]
            
            windows.append({
                'window': len(windows) + 1,
                'train_start': train_data.index[0],
                'train_end': train_data.index[-1],
                'test_start': test_data.index[0],
                'test_end': test_data.index[-1],
                'train_size': len(train_data),
                'test_size': len(test_data)
            })
        
        return {
            'windows': windows,
            'summary': f"Completed {len(windows)} walk-forward windows"
        }
        
    except Exception as e:
        print(f"⚠️ Walk-forward analysis failed: {e}")
        return {'error': str(e)}


def run_monte_carlo_analysis(data: pd.DataFrame) -> Dict[str, Any]:
    """Run Monte Carlo analysis."""
    try:
        import numpy as np
        
        print(f"🎲 Monte Carlo analysis on {len(data)} bars")
        
        # Simple Monte Carlo: shuffle returns
        returns = data['close'].pct_change().dropna()
        num_simulations = 50  # Reduced for testing
        
        simulations = []
        for i in range(num_simulations):
            # Shuffle returns
            shuffled_returns = np.random.permutation(returns)
            
            # Calculate cumulative return
            cum_return = (1 + shuffled_returns).prod() - 1
            
            simulations.append({
                'simulation': i + 1,
                'total_return': cum_return * 100,
                'volatility': shuffled_returns.std() * np.sqrt(252) * 100
            })
        
        # Calculate statistics
        sim_returns = [s['total_return'] for s in simulations]
        
        return {
            'simulations': simulations,
            'statistics': {
                'mean_return': np.mean(sim_returns),
                'std_return': np.std(sim_returns),
                'min_return': np.min(sim_returns),
                'max_return': np.max(sim_returns)
            },
            'summary': f"Completed {num_simulations} Monte Carlo simulations"
        }
        
    except Exception as e:
        print(f"⚠️ Monte Carlo analysis failed: {e}")
        return {'error': str(e)}
