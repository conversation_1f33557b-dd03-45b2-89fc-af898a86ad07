# %%
# Current future warning with pandas
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import os
import numpy as np
import pandas as pd
import pickle
from backtesting import Backtest, Strategy
from backtesting.lib import resample_apply
import matplotlib.pyplot as plt


# %% [markdown]
# # Loading Data

# %%
data = pd.read_csv("BTCUSDT-1m.csv", usecols = [0,1,2,3,4])
data["date"] = pd.to_datetime(data["date"], unit="ms")
data.columns = ["Date","Open","High","Low","Close"]
data.set_index("Date", inplace=True)

# %% [markdown]
# # Strategy Class

# %%
class MomentumStrategy(Strategy):

    small_threshold = 0
    large_threshold = 3

    def momentum(self, data):
        return data.pct_change(periods=7).to_numpy() * 100

    def init(self):
        self.pct_change_long = resample_apply("2h" , self.momentum, self.data.Close.s)
        self.pct_change_short = resample_apply("30T" , self.momentum, self.data.Close.s)

    def next(self):

        change_long = self.pct_change_long[-1]
        change_short = self.pct_change_short[-1]

        if self.position:

            if self.position.is_long and change_short < self.small_threshold:
                    self.position.close()
            elif self.position.is_short and change_short > -1*self.small_threshold:
                    self.position.close()
        else:
            if change_long > self.large_threshold and change_short > self.large_threshold:
                self.buy()
            elif change_long < -1*self.large_threshold and change_short < -1*self.small_threshold:
                self.sell()

# %% [markdown]
# # Test Run

# %%

# Useful for just running strategy once
bt = Backtest(data[ data.index < "2022-02-01" ], MomentumStrategy, cash=10_000_000, commission = 0.002)

stats = bt.optimize(
        small_threshold = list(np.arange(0,1,0.1)),
        large_threshold = list(np.arange(1,3,0.2)),
        maximize='Equity Final [$]')

small_threshold = stats._strategy.small_threshold
large_threshold = stats._strategy.large_threshold

print(stats)
print(stats._strategy)

print(small_threshold, large_threshold)
print(stats._equity_curve)

stats = bt.run()
bt.plot()

# %% [markdown]
# # Walk forward

# %%
def walk_forward(
        strategy,
        data_full,
        warmup_bars,
        lookback_bars=28*1440,
        validation_bars=7*1440,
        cash=10_000_000, 
        commission=0.002):

    stats_master = []

    for i in range(lookback_bars, len(data_full)-validation_bars, validation_bars):

        print(i)

        # To do anchored walk-forward, just set the first slice here to 0
        sample_data = data_full.iloc[i-lookback_bars: i]

        bt_training = Backtest(sample_data, strategy, cash=cash, commission=commission)
        stats_training = bt_training.optimize(
                small_threshold = list(np.arange(0,1,0.1)),
                large_threshold = list(np.arange(1,3,0.2)),
                maximize='Equity Final [$]')

        small_threshold = stats_training._strategy.small_threshold
        large_threshold = stats_training._strategy.large_threshold

        
        validation_data = data_full.iloc[i-warmup_bars:i+validation_bars]
        bt_validation = Backtest(validation_data, strategy, cash=cash, commission=commission)
        stats_validation = bt_validation.run(
                small_threshold = small_threshold,
                large_threshold = large_threshold)

        stats_master.append(stats_validation)

    return stats_master

# %% [markdown]
# ## Run walk-forward

# %%
lookback_bars = 28*1440
validation_bars = 7*1440
warmup_bars = 14*60

if os.path.exists("stats.pickle"):
    with open("stats.pickle", "rb") as f:
        stats = pickle.load(f)
else:
    stats = walk_forward(MomentumStrategy, data, warmup_bars = warmup_bars)
    with open("stats.pickle", "wb") as f:
        pickle.dump(stats, f)

# %% [markdown]
# ## Plot stats of a particular test

# %%
def plot_stats(data, stats):
    equity_curve = stats._equity_curve
    aligned_data = data.reindex(equity_curve.index)
    bt = Backtest(aligned_data, MomentumStrategy, cash=10_000_000, commission=0.002)
    print(stats)
    bt.plot(results=stats)

# %%
plot_stats(data, stats[1])

# %% [markdown]
# ## Plot full equity curve of all validation tests

# %%
def plot_full_equity_curve(data, stats_list, warmup_bars, lookback_bars, overlay_price = True):
    equity_curves = [x["_equity_curve"].iloc[warmup_bars:] for x in stats_list]

    combined = pd.Series()
    for curve in equity_curves:

        # Need to normalise each equity curve to connect them up
        if len(combined) == 0:
            combined = curve["Equity"]/1e7
        else:
            combined = pd.concat([combined, (curve["Equity"]/1e7)*combined.iloc[-1]])

    last_date = combined.index[-1]
    aligned_price_data = data[data.index <= last_date].iloc[lookback_bars:]

    plt.style.use('fivethirtyeight')
    fig, ax1 = plt.subplots()
    # get rid of grid on graph
    ax1.grid(False)
    equity_line, = ax1.plot(combined.index, combined, color = "orange", label = "Equity")

    if overlay_price:
        ax2 = ax1.twinx()
        ax2.grid(False)
        price_line, = ax2.plot(aligned_price_data.index, aligned_price_data.Close, label = "BTCUSDT")
        ax1.legend(handles=[equity_line, price_line])
    else:
        ax1.legend(handles=[equity_line])

    plt.show()

# %%
plot_full_equity_curve(data, stats, warmup_bars = warmup_bars,
                       lookback_bars = lookback_bars, overlay_price = True)

# %% [markdown]
# ## Plot Flow graph of training vs test data

# %%
def plot_split_graph(
        data,
        lookback_bars=28*1440,
        validation_bars=7*1440,
        anchor = False):

    """
    Plot the flow diagram of the training vs test data
    """

    fig, ax = plt.subplots()
    fig.set_figwidth(12)

    ranges = list(range(lookback_bars, len(data)-validation_bars, validation_bars))

    for i in range(len(ranges)):

        # To do anchored walk-forward, just set the first slice here to 0

        if anchor == True:
            sample_data = data.iloc[0: ranges[i]]
        else:
            sample_data = data.iloc[ranges[i]-lookback_bars: ranges[i]]

        validation_data = data.iloc[ranges[i]:ranges[i]+validation_bars]

        plt.fill_between(sample_data.index, 
                [len(ranges) - i - 0.5]*len(sample_data.index),
                [len(ranges) - i + 0.5]*len(sample_data.index),
                color = "blue")
        plt.fill_between(validation_data.index, 
                [len(ranges) - i - 0.5]*len(validation_data.index),
                [len(ranges) - i + 0.5]*len(validation_data.index),
                color = "orange")

    plt.show()

# %%
plot_split_graph(data, lookback_bars=lookback_bars, 
        validation_bars=validation_bars, anchor = False) 

# %%



