name: "vectorbt_strategy"
parameters:
  # Portfolio settings
  cash: 500000
  fees: 0.0004
  slippage: 0.001
  initial_cash: 500000
  
  # Bollinger Bands parameters
  bbands_period: 20
  bbands_std: 2.0
  
  # ADX parameters
  adx_period: 14
  adx_threshold: 20
  adx_threshold_filter: 60
  
  # SMA parameters
  sma_period: 200
  
  # Risk management parameters
  atr_period: 14
  atr_mult: 1.0
  risk_pct: 0.02
  max_side_exposure: 0.30
  
  # DCA parameters
  dca_size_increment: 0.01
  max_dca_size: 0.10
  
  # Multi-timeframe settings
  required_timeframes:
    - "1h"

  # Training/Testing split ratio
  split_ratio: 0.7

optimization_grid:
  bbands_period: [15, 20, 25]
  bbands_std: [1.5, 2.0, 2.5]
  adx_threshold: [15, 20, 25]
  adx_threshold_filter: [50, 60, 70]
  sma_period: [150, 200, 250]
  risk_pct: [0.01, 0.02, 0.03]

analysis_settings:
  monte_carlo_runs: 100
  optimization:
    enable_parallel: true
    max_workers: 4
    early_stopping: true
    early_stopping_patience: 10

csv_path:
  - "data/BTCUSD_1h_2011-2025.csv"
  - "data/EURUSD_1H_2009-2025.csv"