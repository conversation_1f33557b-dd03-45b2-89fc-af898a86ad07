# %%
# ===== IMPORTS =====
import pandas as pd
import numpy as np
import vectorbt as vbt
import pandas_ta as ta

# %%
# ===== CONFIGURATION =====
SYMBOLS = ["BTC-USD", "ETH-USD", "SOL-USD", "AVAX-USD", "MATIC-USD"]
START_DATE = "2023-01-01"
END_DATE = "2025-07-13"
SELECTION_END_DATE = "2023-02-01" #avoid look ahead bias selection

# --- Multi-Timeframe Settings ---
# Timeframes to download and analyze. The first is the primary timeframe for trading.
TIMEFRAMES = ["1D", "1wk"] 
# Which conditions to check on which timeframes
TDI_CROSS_TFS = ["1D"]
TDI_TREND_TFS = ["1wk"] # Corrected: Use the weekly timeframe for the trend filter
TDI_ANGLE_TFS = ["1D"]

# --- TDI Parameters ---
RSI_PERIOD = 21
TDI_FAST_PERIOD = 2
TDI_SLOW_PERIOD = 7
TDI_MIDDLE_PERIOD = 34
TDI_ANGLE_MIN = 20
TDI_ANGLE_MAX = 80
TDI_SHIFT = 1

# --- SL/TP Parameters ---
PIVOT_NUMBER = 2
TARGET_PROBABILITY = 50
SL_TP_DISTANCE_MIN_PCTG = 0.005

# --- Portfolio & Sizing Parameters ---
INITIAL_CASH = 10000
FEE = 0.000045
ATR_PERIOD = 14
VOL_ADJ_TARGET_RISK = 0.02
MAX_POSITION_SIZE = 1.5

# %%
# ===== ASSET SELECTION =====
def select_best_asset(symbols, start_date, end_date, primary_tf):
    """Selects the best asset based on a simple risk-adjusted return score over a lookback period."""
    scores = {}
    print(f"--- Asset Selection based on data from {start_date} to {end_date} on {primary_tf} ---")
    for symbol in symbols:
        try:
            data = vbt.YFData.download(symbol, start=start_date, end=end_date, interval=primary_tf).get('Close')
            if data.empty:
                continue
            returns = data.pct_change().dropna()
            # Annualized Sharpe-like ratio
            score = (returns.mean() / returns.std()) * np.sqrt(252) if returns.std() > 0 else 0
            scores[symbol] = score
            print(f"{symbol}: Annualized Vol-Adj Return Score = {score:.4f}")
        except Exception as e:
            print(f"Could not score {symbol}: {e}")
    if not scores:
        print("Could not score any asset, falling back to SOL-USD")
        return "SOL-USD"
    best_symbol = max(scores, key=scores.get)
    print(f"\nSelected asset: {best_symbol} (Score: {scores[best_symbol]:.4f})")
    return best_symbol

SYMBOL = select_best_asset(SYMBOLS,START_DATE, SELECTION_END_DATE, TIMEFRAMES[0])

# %%
# ===== DATA PREPARATION & INDICATORS =====
print("\n--- Data Loading and Indicator Calculation ---")
all_data = {}
for tf in TIMEFRAMES:
    print(f"Fetching {SYMBOL} data for {tf} timeframe...")
    # Fetch slightly more data for indicator calculations
    extended_start = pd.to_datetime(START_DATE) - pd.Timedelta(days=100)
    
    # Download data; vbt returns a DataFrame for a single symbol
    df_raw = vbt.YFData.download(SYMBOL, start=extended_start, end=END_DATE, interval=tf).get()
    
    # Ensure df_raw is a DataFrame
    if isinstance(df_raw, tuple):
        df = df_raw[0].copy()
    else:
        df = df_raw.copy()

    df.columns = [c.lower() for c in df.columns]

    # Calculate Indicators
    df['rsi'] = ta.rsi(df['close'], length=RSI_PERIOD)
    df['tdi_fast_ma'] = ta.sma(df['rsi'], length=TDI_FAST_PERIOD)
    df['tdi_slow_ma'] = ta.sma(df['rsi'], length=TDI_SLOW_PERIOD)
    df['tdi_middle_ma'] = ta.sma(df['rsi'], length=TDI_MIDDLE_PERIOD)
    df['tdi_fast_angle'] = np.degrees(np.arctan(df['tdi_fast_ma'].diff()))
    df['tdi_slow_angle'] = np.degrees(np.arctan(df['tdi_slow_ma'].diff()))
    df['tdi_angle'] = (df['tdi_slow_angle'] + (df['tdi_fast_angle'] * RSI_PERIOD / TDI_MIDDLE_PERIOD)) / (1 + RSI_PERIOD / TDI_MIDDLE_PERIOD)
    df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=ATR_PERIOD)
    
    all_data[tf] = df.dropna()

primary_tf = TIMEFRAMES[0]
data = all_data[primary_tf].copy() # Main dataframe for the backtest
print(f"Primary data loaded with {len(data)} rows.")

# %%
# ===== MULTI-TIMEFRAME SIGNAL GENERATION =====
print("\n--- Generating Multi-Timeframe Signals ---")

# Start with a Series of True, we will 'AND' conditions to it
final_long_conditions = pd.Series(True, index=data.index)
final_short_conditions = pd.Series(True, index=data.index)

timeframe_config = {
    'cross': set(TDI_CROSS_TFS),
    'trend': set(TDI_TREND_TFS),
    'angle': set(TDI_ANGLE_TFS),
}

for tf, df in all_data.items():
    print(f"Processing conditions for {tf}...")
    
    # --- Generate signals for this timeframe ---
    long_signal = pd.Series(True, index=df.index)
    short_signal = pd.Series(True, index=df.index)

    # Pre-shift for clarity and performance
    shifted_fast_ma = df['tdi_fast_ma'].shift(TDI_SHIFT)
    shifted_slow_ma = df['tdi_slow_ma'].shift(TDI_SHIFT)
    shifted_middle_ma = df['tdi_middle_ma'].shift(TDI_SHIFT)
    prev_fast_ma = df['tdi_fast_ma'].shift(TDI_SHIFT + 1)
    prev_slow_ma = df['tdi_slow_ma'].shift(TDI_SHIFT + 1)
    prev_middle_ma = df['tdi_middle_ma'].shift(TDI_SHIFT + 1)
    shifted_angle = df['tdi_angle'].shift(TDI_SHIFT)

    if tf in timeframe_config['cross']:
        long_signal &= (prev_fast_ma < prev_slow_ma) & (shifted_fast_ma > shifted_slow_ma)
        short_signal &= (prev_fast_ma > prev_slow_ma) & (shifted_fast_ma < shifted_slow_ma)
    
    if tf in timeframe_config['trend']:
        long_signal &= shifted_middle_ma > prev_middle_ma
        short_signal &= shifted_middle_ma < prev_middle_ma

    if tf in timeframe_config['angle']:
        long_signal &= (shifted_angle >= TDI_ANGLE_MIN) & (shifted_angle <= TDI_ANGLE_MAX)
        short_signal &= (shifted_angle <= -TDI_ANGLE_MIN) & (shifted_angle >= -TDI_ANGLE_MAX)
        
    # --- Align signals to primary timeframe ---
    aligned_long = long_signal.reindex(data.index, method='ffill').fillna(False)
    aligned_short = short_signal.reindex(data.index, method='ffill').fillna(False)
          
    final_long_conditions &= aligned_long
    final_short_conditions &= aligned_short

# Drop any remaining NaNs from the final conditions and align main dataframe
common_index = data.index.intersection(final_long_conditions.dropna().index).intersection(final_short_conditions.dropna().index)
data = data.loc[common_index]
final_long_conditions = final_long_conditions.loc[common_index]
final_short_conditions = final_short_conditions.loc[common_index]

# %%
# ===== PIVOT-BASED SL/TP AND FINAL SIGNALS =====
def calculate_vectorized_stops_as_pct(data, pivot_number, target_probability, min_dist_pctg):
    """
    Calculates dynamic SL/TP levels based on pivots for EVERY bar and returns them 
    as a percentage of the close price. This is a fully vectorized approach.
    """
    # 1. Calculate all pivot levels (vectorized)
    prev_high, prev_low, prev_close = data['high'].shift(1), data['low'].shift(1), data['close'].shift(1)
    price = data['close']
    pivot = (prev_high + prev_low + prev_close) / 3
    supports = [2 * pivot - prev_high if i == 1 else pivot - (prev_high - prev_low) * (i - 1) for i in range(1, pivot_number + 1)]
    resistances = [2 * pivot - prev_low if i == 1 else pivot + (prev_high - prev_low) * (i - 1) for i in range(1, pivot_number + 1)]

    # 2. Find best SL/TP for LONG positions
    sl_df_long = pd.concat([s for s in supports for _ in range(pivot_number)], axis=1)
    tp_df_long = pd.concat([r for _ in range(pivot_number) for r in resistances], axis=1)
    sl_size_long = price.values[:, None] - sl_df_long.values
    tp_size_long = tp_df_long.values - price.values[:, None]
    valid_mask = (sl_size_long > (price * min_dist_pctg).values[:, None]) & (tp_size_long > (price * min_dist_pctg).values[:, None])
    total_size_long = sl_size_long + tp_size_long
    mask = (total_size_long > 0) & valid_mask
    proba_long = np.full(mask.shape, np.nan)
    proba_long[mask] = (tp_size_long[mask] / total_size_long[mask]) * 100
    proba_diff_long = np.abs(proba_long - target_probability)
    all_nan_mask = np.all(np.isnan(proba_diff_long), axis=1)
    best_combo_idx_long = np.full(proba_diff_long.shape[0], np.nan)
    if np.any(~all_nan_mask):
        best_combo_idx_long[~all_nan_mask] = np.nanargmin(proba_diff_long[~all_nan_mask], axis=1)

    # 3. Find best SL/TP for SHORT positions
    sl_df_short = pd.concat([r for r in resistances for _ in range(pivot_number)], axis=1)
    tp_df_short = pd.concat([s for _ in range(pivot_number) for s in supports], axis=1)
    sl_size_short = sl_df_short.values - price.values[:, None]
    tp_size_short = price.values[:, None] - tp_df_short.values
    valid_mask_short = (sl_size_short > (price * min_dist_pctg).values[:, None]) & (tp_size_short > (price * min_dist_pctg).values[:, None])
    total_size_short = sl_size_short + tp_size_short
    mask_short = (total_size_short > 0) & valid_mask_short
    proba_short = np.full(mask_short.shape, np.nan)
    proba_short[mask_short] = (tp_size_short[mask_short] / total_size_short[mask_short]) * 100
    proba_diff_short = np.abs(proba_short - target_probability)
    all_nan_mask_short = np.all(np.isnan(proba_diff_short), axis=1)
    best_combo_idx_short = np.full(proba_diff_short.shape[0], np.nan)
    if np.any(~all_nan_mask_short):
        best_combo_idx_short[~all_nan_mask_short] = np.nanargmin(proba_diff_short[~all_nan_mask_short], axis=1)

    # 4. Helper to look up the best levels and convert to percentages
    def lookup_and_convert(df, indices):
        valid_indices = indices.dropna()
        if valid_indices.empty: return pd.Series(np.nan, index=df.index)
        row_idx = df.index.get_indexer_for(valid_indices.index)
        col_idx = valid_indices.astype(int).values
        best_levels = pd.Series(df.values[row_idx, col_idx], index=valid_indices.index).reindex(df.index)
        price_at_idx = price.loc[best_levels.index]
        return (best_levels - price_at_idx).abs() / price_at_idx

    sl_long_pct = lookup_and_convert(sl_df_long, pd.Series(best_combo_idx_long, index=data.index))
    tp_long_pct = lookup_and_convert(tp_df_long, pd.Series(best_combo_idx_long, index=data.index))
    sl_short_pct = lookup_and_convert(sl_df_short, pd.Series(best_combo_idx_short, index=data.index))
    tp_short_pct = lookup_and_convert(tp_df_short, pd.Series(best_combo_idx_short, index=data.index))
    
    return sl_long_pct, tp_long_pct, sl_short_pct, tp_short_pct

# Calculate SL/TP percentages for ALL bars
sl_long_pct, tp_long_pct, sl_short_pct, tp_short_pct = calculate_vectorized_stops_as_pct(data, PIVOT_NUMBER, TARGET_PROBABILITY, SL_TP_DISTANCE_MIN_PCTG)

# Use vectorbt's built-in function to generate chained, clean entries and corresponding exits
print("\n--- Generating Cleaned Signals with vbt ---")
long_entries, long_exits = final_long_conditions.vbt.signals.generate_ohlc_stop_exits(
    data['open'], data['high'], data['low'], data['close'],
    sl_stop=sl_long_pct,
    tp_stop=tp_long_pct,
    chain=True
)

short_entries, short_exits = final_short_conditions.vbt.signals.generate_ohlc_stop_exits(
    data['open'], data['high'], data['low'], data['close'],
    sl_stop=sl_short_pct,
    tp_stop=tp_short_pct,
    chain=True
)

# %%
# ===== SIZING =====
print(f"\nFinal cleaned signals: {long_entries.sum()} long, {short_entries.sum()} short")

# Sizing based on value
# Note: This sizing method risks a fixed percentage of the *initial* capital on each trade.
# For a method that risks a percentage of *current* equity, Portfolio.from_order_func is required.
target_value_at_risk = VOL_ADJ_TARGET_RISK * INITIAL_CASH
# ATR at the time of the *cleaned* entry signal
atr_at_entry = data['atr'][long_entries | short_entries]
close_at_entry = data['close'][long_entries | short_entries]

size_in_value = (target_value_at_risk / atr_at_entry) * close_at_entry
size_in_value = size_in_value.clip(upper=MAX_POSITION_SIZE * INITIAL_CASH)

size_array = pd.Series(np.nan, index=data.index)
size_array.loc[long_entries | short_entries] = size_in_value

# %%
# ===== PORTFOLIO BACKTEST =====
portfolio = vbt.Portfolio.from_signals(
    close=data['close'],
    entries=long_entries,
    exits=long_exits,
    short_entries=short_entries,
    short_exits=short_exits,
    size=size_array,
    size_type='value',
    init_cash=INITIAL_CASH,
    fees=FEE,
    freq=primary_tf
)

# %%
# ===== STATS & PLOTTING =====
vbt.settings.set_theme('dark')
print("\n--- Backtest Stats ---")
print(portfolio.stats())

print("\n--- Plotting Results ---")
fig = portfolio.plot(
    subplot_settings={'orders': {'close_trace_kwargs': {'visible': False}}}
)
fig = data.vbt.ohlcv.plot(
    plot_type='candlestick', 
    fig=fig, 
    show_volume=False, 
    xaxis_rangeslider_visible=False
)
fig.update_layout(
    width=None, 
    height=600, 
    title_text=f"TDI Multi-Timeframe Strategy - {SYMBOL}"
).show()

# TDI indicators plot
tdi_data = all_data[primary_tf][['tdi_fast_ma', 'tdi_slow_ma', 'tdi_middle_ma', 'rsi']].rename(columns={
    'tdi_fast_ma': 'TDI Fast MA',
    'tdi_slow_ma': 'TDI Slow MA', 
    'tdi_middle_ma': 'TDI Middle MA',
    'rsi': 'RSI'
})
tdi_fig = tdi_data.vbt.plot(
    title_text="TDI Indicators (Primary Timeframe)",
    yaxis_title="RSI Values"
)
tdi_fig.add_hline(y=70, line_dash="dot", line_color="red", opacity=0.5)
tdi_fig.add_hline(y=30, line_dash="dot", line_color="green", opacity=0.5)
tdi_fig.show()

# Trade analysis
portfolio.plot(
    subplots=['trades', 'trade_pnl'],
    subplot_settings={
        'trades': dict(title='Trades Timeline'),
        'trade_pnl': dict(title='Individual Trade P&L')
    }
).update_layout(
    width=None, 
    height=None, 
    title_text="TDI Strategy - Trade Analysis"
).show()