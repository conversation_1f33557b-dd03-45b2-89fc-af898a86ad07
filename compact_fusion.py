"""
Optimized portfolio fusion - VectorBT core subplots in unified view.
Enhanced with price data on orders subplot and streamlined code.
"""

import warnings
warnings.filterwarnings("ignore")

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import vectorbt as vbt

def create_fused_portfolio_plot(portfolios: dict, strategy_name: str = "Strategy") -> go.Figure:
    """Create optimized fused plot with VectorBT's core components including price data."""
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=['Portfolio Value', 'Orders & Price', 'PnL Distribution', 'Drawdown'],
        vertical_spacing=0.1, horizontal_spacing=0.1
    )
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    for idx, (name, pf) in enumerate(portfolios.items()):
        color = colors[idx % len(colors)]
        
        try:
            # Extract all data efficiently
            value, orders, returns, drawdown = pf.value(), pf.orders.records_readable, pf.returns(), pf.drawdown()
            
            # 1. Portfolio Value
            fig.add_trace(go.Scatter(
                x=value.index, y=value.values, mode='lines',
                name=name, line={'color': color, 'width': 2}
            ), row=1, col=1)
            
            # 2. Add price data for this portfolio to orders subplot
            try:
                # Try multiple ways to get the original price data
                price_data = None
                if hasattr(pf, '_price_data'):  # Our manually stored data
                    price_data = pf._price_data
                elif hasattr(pf, 'close'):
                    price_data = pf.close
                elif hasattr(pf, '_close'):
                    price_data = pf._close
                elif hasattr(pf, 'wrapper') and hasattr(pf.wrapper, 'close'):
                    price_data = pf.wrapper.close
                
                if price_data is not None:
                    fig.add_trace(go.Scatter(
                        x=price_data.index, y=price_data.values, mode='lines',
                        name=f'{name} Price', line={'color': color, 'width': 2}, 
                        opacity=0.7, showlegend=(idx==0)
                    ), row=1, col=2)
            except Exception:
                pass
            
            # 3. Orders with price context
            if len(orders) > 0:
                for side, symbol, marker_color in [('Buy', 'triangle-up', 'green'), ('Sell', 'triangle-down', 'red')]:
                    side_orders = orders[orders['Side'] == side]
                    if len(side_orders) > 0:
                        fig.add_trace(go.Scatter(
                            x=side_orders['Timestamp'], y=side_orders['Price'], mode='markers',
                            marker={'symbol': symbol, 'color': marker_color, 'size': 10},
                            name=f'{name} {side}', showlegend=(idx==0)
                        ), row=1, col=2)
            
            # 4. PnL Distribution (histogram of trade PnLs)
            try:
                trades = pf.trades.records_readable
                if len(trades) > 0 and 'PnL' in trades.columns:
                    trade_pnls = trades['PnL'].dropna()
                    if len(trade_pnls) > 0:
                        fig.add_trace(go.Histogram(
                            x=trade_pnls, name=f'{name} PnL', 
                            marker={'color': color}, opacity=0.7,
                            showlegend=False, nbinsx=20
                        ), row=2, col=1)
            except Exception:
                # Fallback to returns if trades not available
                fig.add_trace(go.Histogram(
                    x=returns.dropna(), name=f'{name} Returns', 
                    marker={'color': color}, opacity=0.7,
                    showlegend=False, nbinsx=30
                ), row=2, col=1)
            
            # 5. Drawdown with proper RGBA fill
            hex_color = color.lstrip('#')
            r, g, b = int(hex_color[0:2], 16), int(hex_color[2:4], 16), int(hex_color[4:6], 16)
            fig.add_trace(go.Scatter(
                x=drawdown.index, y=drawdown.values, mode='lines', fill='tonexty',
                line={'color': color, 'width': 1}, fillcolor=f'rgba({r},{g},{b},0.3)',
                showlegend=False
            ), row=2, col=2)
            
        except Exception as e:
            print(f"⚠️ Failed to process {name}: {e}")
    
    # Streamlined layout update
    fig.update_layout(
        title=f"📊 {strategy_name} - Fused Analysis",
        template='plotly_dark', height=600, width=1000, showlegend=True
    )
    
    # Batch axis updates
    axis_labels = [("Value", "Price"), ("Frequency", "Drawdown (%)")]
    for row, (left_label, right_label) in enumerate(axis_labels, 1):
        fig.update_yaxes(title_text=left_label, row=row, col=1)
        fig.update_yaxes(title_text=right_label, row=row, col=2)
    
    return fig

def quick_test():
    """Quick test with sample data - different price series for each portfolio."""
    dates = pd.date_range('2023-01-01', '2023-03-31', freq='D')
    np.random.seed(42)
    
    portfolios = {}
    for idx, name in enumerate(['EURUSD_Strategy', 'GBPUSD_Strategy']):
        # Create different price series for each portfolio
        base_price = 100 + idx * 20  # Different starting prices
        price_volatility = 1 + idx * 0.5  # Different volatilities
        price = pd.Series(
            base_price + np.cumsum(np.random.normal(0, price_volatility, len(dates))), 
            index=dates
        )
        
        # More frequent trading to get better PnL distribution
        trade_frequency = [0.6, 0.25, 0.15] if idx == 0 else [0.5, 0.3, 0.2]
        size = pd.Series(
            np.random.choice([0, 0.2, -0.2], len(dates), p=trade_frequency), 
            index=dates
        )
        
        # Create portfolio and manually store price data for access
        pf = vbt.Portfolio.from_orders(price, size, init_cash=10000, freq='D')
        
        # Manually attach the price data to ensure it's accessible
        pf._price_data = price  # Store for our plotting function
        
        portfolios[name] = pf
    
    # Create and show fused plot
    fig = create_fused_portfolio_plot(portfolios, "Multi-Asset Strategy")
    if fig:
        fig.show()
        print("✅ Compact fusion plot with price lines and PnL distribution created!")
        
        # Show stats for verification
        for name, pf in portfolios.items():
            stats = pf.stats()
            trades_count = stats['Total Trades']
            print(f"📊 {name}: Return={stats['Total Return [%]']:.2f}%, Trades={trades_count}")
    else:
        print("❌ Failed to create plot")

if __name__ == "__main__":
    quick_test()