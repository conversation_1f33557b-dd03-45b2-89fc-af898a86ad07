# ============================================================
# Configuration for the Traders Dynamic Index (TDI) Strategy
# ============================================================
name: "tdi_strategy"

# ------------------------------------------------------------
# Strategy Parameters
# These are the base settings for the strategy.
# The optimizer will override these with the ranges below.
# ------------------------------------------------------------
parameters:
  # Pivot Point Parameters
  pivot_timeframe: "1D"  # Timeframe for pivot point calculation
  pivot_number: 2
  target_probability: 50
  
  # TDI Core Parameters
  rsi_period: 21
  tdi_fast_period: 2
  tdi_slow_period: 7
  tdi_middle_period: 34
  tdi_angle_min: 20
  tdi_angle_max: 80
  
  # Multi-Timeframe Settings
  required_timeframes: ["15m", "30m", "1h", "4h", "1D"]
  
  # Signal Configuration (corresponds to timeframes above)
  # Use only 1h timeframe for simpler, more reliable signals
  tdi_cross_enabled: [false, false, true, false, false]
  tdi_trend_enabled: [false, false, true, false, false]
  tdi_angle_enabled: [false, false, false, false, false]
  tdi_shift: 1
  
  # Risk Management (distances in price units, not pips)
  risk_factor: 1.0
  spread_max: 2.0
  sl_distance_min: 0.001  # ~10 pips for EURUSD
  tp_distance_min: 0.001  # ~10 pips for EURUSD

# ------------------------------------------------------------
# Optimization Parameter Ranges (Reduced for efficiency)
# The grid search will test all combinations of these values.
# Total combinations: 2 × 2 × 2 × 2 × 2 = 32 combinations
# ------------------------------------------------------------
optimization_grid:
  # Core TDI parameters (most impactful)
  rsi_period: [21, 34]
  tdi_fast_period: [2, 3]
  tdi_slow_period: [7, 10]
  
  # Pivot settings (key for entry/exit)
  target_probability: [45, 55]
  pivot_number: [2, 3]


csv_path:
  - "data/EURUSD_15m_2021-2025.csv"
  - "data/EURUSD_30m_2017_2025.csv"
  - "data/EURUSD_1H_2009-2025.csv"
  - "data/EURUSD_4H_2009-2025.csv"
  - "data/EURUSD_1D_2009-2025.csv"

